// Shadcn Block Templates for AI Generation
// Pre-built templates using shadcn/ui components for intelligent block generation

export interface ShadcnBlockTemplate {
  id: string
  name: string
  description: string
  category: 'hero' | 'feature' | 'testimonial' | 'pricing' | 'contact' | 'product' | 'content' | 'navigation' | 'footer'
  components: string[]
  complexity: 'simple' | 'moderate' | 'complex'
  jsx: string
  props: Record<string, any>
  styling: {
    theme: string
    colorScheme: string
    spacing: string
    borderRadius: string
  }
  variants: Array<{
    name: string
    props: Record<string, any>
    description: string
  }>
  useCases: string[]
  tags: string[]
}

// Hero Section Templates
export const heroTemplates: ShadcnBlockTemplate[] = [
  {
    id: 'hero-card-cta',
    name: 'Hero with Card and CTA',
    description: 'Modern hero section with card layout and call-to-action button',
    category: 'hero',
    components: ['Card', 'Button', 'Badge'],
    complexity: 'moderate',
    jsx: `
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { cn } from '@/lib/utils'

interface HeroCardCTAProps {
  title?: string
  subtitle?: string
  description?: string
  ctaText?: string
  ctaVariant?: 'default' | 'outline' | 'secondary'
  badgeText?: string
  className?: string
}

export function HeroCardCTA({
  title = "Welcome to Our Platform",
  subtitle = "Build Amazing Things",
  description = "Create beautiful, responsive websites with our powerful tools and components.",
  ctaText = "Get Started",
  ctaVariant = "default",
  badgeText = "New",
  className
}: HeroCardCTAProps) {
  return (
    <div className={cn("flex items-center justify-center min-h-[500px] p-6", className)}>
      <Card className="max-w-2xl w-full text-center">
        <CardHeader className="space-y-4">
          {badgeText && (
            <div className="flex justify-center">
              <Badge variant="secondary">{badgeText}</Badge>
            </div>
          )}
          <CardTitle className="text-4xl font-bold tracking-tight">
            {title}
          </CardTitle>
          <p className="text-xl text-muted-foreground">{subtitle}</p>
        </CardHeader>
        <CardContent className="space-y-6">
          <p className="text-lg text-muted-foreground leading-relaxed">
            {description}
          </p>
          <Button size="lg" variant={ctaVariant} className="px-8">
            {ctaText}
          </Button>
        </CardContent>
      </Card>
    </div>
  )
}`,
    props: {
      title: 'Welcome to Our Platform',
      subtitle: 'Build Amazing Things',
      description: 'Create beautiful, responsive websites with our powerful tools and components.',
      ctaText: 'Get Started',
      ctaVariant: 'default',
      badgeText: 'New'
    },
    styling: {
      theme: 'default',
      colorScheme: 'blue',
      spacing: 'normal',
      borderRadius: 'md'
    },
    variants: [
      {
        name: 'minimal',
        props: { badgeText: '', ctaVariant: 'outline' },
        description: 'Clean minimal version without badge'
      },
      {
        name: 'bold',
        props: { ctaVariant: 'default', badgeText: 'Featured' },
        description: 'Bold version with featured badge'
      }
    ],
    useCases: ['landing pages', 'product launches', 'service introductions'],
    tags: ['hero', 'cta', 'card', 'modern']
  }
]

// Feature Section Templates
export const featureTemplates: ShadcnBlockTemplate[] = [
  {
    id: 'feature-grid-cards',
    name: 'Feature Grid with Cards',
    description: 'Grid layout showcasing features using card components',
    category: 'feature',
    components: ['Card', 'Badge', 'Separator'],
    complexity: 'moderate',
    jsx: `
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { cn } from '@/lib/utils'

interface Feature {
  title: string
  description: string
  badge?: string
  icon?: React.ReactNode
}

interface FeatureGridCardsProps {
  title?: string
  subtitle?: string
  features?: Feature[]
  columns?: number
  className?: string
}

export function FeatureGridCards({
  title = "Our Features",
  subtitle = "Everything you need to succeed",
  features = [
    { title: "Fast Performance", description: "Lightning-fast loading times", badge: "New" },
    { title: "Secure", description: "Enterprise-grade security", badge: "Pro" },
    { title: "Scalable", description: "Grows with your business" }
  ],
  columns = 3,
  className
}: FeatureGridCardsProps) {
  return (
    <div className={cn("py-16 px-6", className)}>
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight mb-4">{title}</h2>
          <p className="text-lg text-muted-foreground">{subtitle}</p>
          <Separator className="mt-8 max-w-xs mx-auto" />
        </div>
        
        <div className={cn(
          "grid gap-6",
          columns === 2 && "grid-cols-1 md:grid-cols-2",
          columns === 3 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
          columns === 4 && "grid-cols-1 md:grid-cols-2 lg:grid-cols-4"
        )}>
          {features.map((feature, index) => (
            <Card key={index} className="h-full">
              <CardHeader>
                <div className="flex items-center justify-between">
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                  {feature.badge && (
                    <Badge variant="secondary">{feature.badge}</Badge>
                  )}
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">{feature.description}</p>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}`,
    props: {
      title: 'Our Features',
      subtitle: 'Everything you need to succeed',
      features: [
        { title: "Fast Performance", description: "Lightning-fast loading times", badge: "New" },
        { title: "Secure", description: "Enterprise-grade security", badge: "Pro" },
        { title: "Scalable", description: "Grows with your business" }
      ],
      columns: 3
    },
    styling: {
      theme: 'default',
      colorScheme: 'blue',
      spacing: 'normal',
      borderRadius: 'md'
    },
    variants: [
      {
        name: 'compact',
        props: { columns: 4 },
        description: '4-column compact layout'
      },
      {
        name: 'detailed',
        props: { columns: 2 },
        description: '2-column detailed layout'
      }
    ],
    useCases: ['feature showcases', 'service listings', 'product benefits'],
    tags: ['features', 'grid', 'cards', 'responsive']
  }
]

// Pricing Section Templates
export const pricingTemplates: ShadcnBlockTemplate[] = [
  {
    id: 'pricing-cards',
    name: 'Pricing Cards',
    description: 'Pricing plans displayed in card format with buttons',
    category: 'pricing',
    components: ['Card', 'Button', 'Badge', 'Separator'],
    complexity: 'complex',
    jsx: `
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Check } from 'lucide-react'
import { cn } from '@/lib/utils'

interface PricingPlan {
  name: string
  price: string
  period: string
  description: string
  features: string[]
  popular?: boolean
  ctaText?: string
}

interface PricingCardsProps {
  title?: string
  subtitle?: string
  plans?: PricingPlan[]
  className?: string
}

export function PricingCards({
  title = "Choose Your Plan",
  subtitle = "Select the perfect plan for your needs",
  plans = [
    {
      name: "Starter",
      price: "$9",
      period: "month",
      description: "Perfect for individuals",
      features: ["5 Projects", "10GB Storage", "Email Support"],
      ctaText: "Get Started"
    },
    {
      name: "Pro",
      price: "$29",
      period: "month",
      description: "Best for growing teams",
      features: ["Unlimited Projects", "100GB Storage", "Priority Support", "Advanced Analytics"],
      popular: true,
      ctaText: "Start Free Trial"
    },
    {
      name: "Enterprise",
      price: "$99",
      period: "month",
      description: "For large organizations",
      features: ["Everything in Pro", "Custom Integrations", "Dedicated Support", "SLA"],
      ctaText: "Contact Sales"
    }
  ],
  className
}: PricingCardsProps) {
  return (
    <div className={cn("py-16 px-6", className)}>
      <div className="max-w-6xl mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold tracking-tight mb-4">{title}</h2>
          <p className="text-lg text-muted-foreground">{subtitle}</p>
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {plans.map((plan, index) => (
            <Card key={index} className={cn(
              "relative",
              plan.popular && "border-primary shadow-lg scale-105"
            )}>
              {plan.popular && (
                <div className="absolute -top-3 left-1/2 transform -translate-x-1/2">
                  <Badge>Most Popular</Badge>
                </div>
              )}
              
              <CardHeader className="text-center">
                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <div className="mt-4">
                  <span className="text-4xl font-bold">{plan.price}</span>
                  <span className="text-muted-foreground">/{plan.period}</span>
                </div>
                <p className="text-muted-foreground">{plan.description}</p>
              </CardHeader>
              
              <CardContent>
                <Separator className="mb-6" />
                <ul className="space-y-3">
                  {plan.features.map((feature, featureIndex) => (
                    <li key={featureIndex} className="flex items-center">
                      <Check className="w-4 h-4 text-green-500 mr-3" />
                      <span className="text-sm">{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              
              <CardFooter>
                <Button 
                  className="w-full" 
                  variant={plan.popular ? "default" : "outline"}
                >
                  {plan.ctaText}
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>
      </div>
    </div>
  )
}`,
    props: {
      title: 'Choose Your Plan',
      subtitle: 'Select the perfect plan for your needs',
      plans: [
        {
          name: "Starter",
          price: "$9",
          period: "month",
          description: "Perfect for individuals",
          features: ["5 Projects", "10GB Storage", "Email Support"],
          ctaText: "Get Started"
        }
      ]
    },
    styling: {
      theme: 'default',
      colorScheme: 'blue',
      spacing: 'normal',
      borderRadius: 'md'
    },
    variants: [
      {
        name: 'simple',
        props: { plans: [] },
        description: 'Simple 2-plan layout'
      },
      {
        name: 'enterprise',
        props: { plans: [] },
        description: 'Enterprise-focused pricing'
      }
    ],
    useCases: ['SaaS pricing', 'subscription plans', 'service tiers'],
    tags: ['pricing', 'plans', 'cards', 'subscription']
  }
]

// Template Registry
export const SHADCN_BLOCK_TEMPLATES = {
  hero: heroTemplates,
  feature: featureTemplates,
  pricing: pricingTemplates,
  // Add more categories as needed
}

// Template Selection Functions
export function getTemplatesByCategory(category: string): ShadcnBlockTemplate[] {
  return SHADCN_BLOCK_TEMPLATES[category as keyof typeof SHADCN_BLOCK_TEMPLATES] || []
}

export function getTemplateById(id: string): ShadcnBlockTemplate | undefined {
  const allTemplates = Object.values(SHADCN_BLOCK_TEMPLATES).flat()
  return allTemplates.find(template => template.id === id)
}

export function getTemplatesByComponents(components: string[]): ShadcnBlockTemplate[] {
  const allTemplates = Object.values(SHADCN_BLOCK_TEMPLATES).flat()
  return allTemplates.filter(template => 
    components.some(component => template.components.includes(component))
  )
}

export function getTemplatesByComplexity(complexity: 'simple' | 'moderate' | 'complex'): ShadcnBlockTemplate[] {
  const allTemplates = Object.values(SHADCN_BLOCK_TEMPLATES).flat()
  return allTemplates.filter(template => template.complexity === complexity)
}

export function searchTemplates(query: string): ShadcnBlockTemplate[] {
  const allTemplates = Object.values(SHADCN_BLOCK_TEMPLATES).flat()
  const searchTerm = query.toLowerCase()
  
  return allTemplates.filter(template => 
    template.name.toLowerCase().includes(searchTerm) ||
    template.description.toLowerCase().includes(searchTerm) ||
    template.tags.some(tag => tag.toLowerCase().includes(searchTerm)) ||
    template.useCases.some(useCase => useCase.toLowerCase().includes(searchTerm))
  )
}
