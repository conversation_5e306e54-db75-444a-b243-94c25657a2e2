'use client'

import React, { useState } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Switch } from '@/components/ui/switch'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Sparkles, 
  Code, 
  Settings, 
  Palette, 
  Layers,
  Zap,
  CheckCircle,
  AlertCircle,
  Info
} from 'lucide-react'
import { cn } from '@/lib/utils'
import { toast } from 'sonner'

interface ShadcnBlockGeneratorDemoProps {
  className?: string
}

export function ShadcnBlockGeneratorDemo({ className }: ShadcnBlockGeneratorDemoProps) {
  const [isGenerating, setIsGenerating] = useState(false)
  const [generatedBlock, setGeneratedBlock] = useState<any>(null)
  const [formData, setFormData] = useState({
    description: 'Modern hero section with call-to-action button and feature highlights',
    blockType: 'hero',
    components: ['Card', 'Button', 'Badge'],
    designStyle: 'modern',
    complexity: 'moderate',
    responsive: true,
    accessibility: true,
    themeSupport: true,
    animations: true
  })

  const blockTypes = [
    { value: 'hero', label: 'Hero Section' },
    { value: 'feature', label: 'Feature Section' },
    { value: 'testimonial', label: 'Testimonial' },
    { value: 'pricing', label: 'Pricing' },
    { value: 'contact', label: 'Contact Form' },
    { value: 'product', label: 'Product Showcase' },
    { value: 'content', label: 'Content Block' },
    { value: 'navigation', label: 'Navigation' },
    { value: 'footer', label: 'Footer' }
  ]

  const designStyles = [
    { value: 'modern', label: 'Modern' },
    { value: 'minimal', label: 'Minimal' },
    { value: 'bold', label: 'Bold' },
    { value: 'elegant', label: 'Elegant' },
    { value: 'playful', label: 'Playful' },
    { value: 'professional', label: 'Professional' }
  ]

  const complexityLevels = [
    { value: 'simple', label: 'Simple' },
    { value: 'moderate', label: 'Moderate' },
    { value: 'complex', label: 'Complex' }
  ]

  const availableComponents = [
    'Card', 'Button', 'Badge', 'Input', 'Select', 'Checkbox', 'Switch',
    'Tabs', 'Dialog', 'Sheet', 'Alert', 'Avatar', 'Separator', 'Progress',
    'Accordion', 'NavigationMenu', 'Breadcrumb', 'Pagination'
  ]

  const handleGenerate = async () => {
    setIsGenerating(true)
    
    try {
      // Simulate API call to shadcn block generation
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Mock generated block result
      const mockResult = {
        success: true,
        block: {
          id: `shadcn-${formData.blockType}-${Date.now()}`,
          name: `${formData.designStyle.charAt(0).toUpperCase() + formData.designStyle.slice(1)} ${formData.blockType.charAt(0).toUpperCase() + formData.blockType.slice(1)}`,
          description: formData.description,
          components: formData.components,
          jsx: generateMockJSX(formData),
          properties: generateMockProperties(formData),
          variants: generateMockVariants(formData)
        },
        recommendations: [
          'Consider adding loading states for better UX',
          'Implement keyboard navigation for accessibility',
          'Add responsive breakpoints for mobile optimization'
        ]
      }
      
      setGeneratedBlock(mockResult)
      toast.success('Block generated successfully!')
      
    } catch (error) {
      toast.error('Failed to generate block')
      console.error('Generation error:', error)
    } finally {
      setIsGenerating(false)
    }
  }

  const handleComponentToggle = (component: string) => {
    setFormData(prev => ({
      ...prev,
      components: prev.components.includes(component)
        ? prev.components.filter(c => c !== component)
        : [...prev.components, component]
    }))
  }

  return (
    <div className={cn("space-y-6", className)}>
      {/* Header */}
      <div className="text-center space-y-2">
        <div className="flex items-center justify-center gap-2">
          <Sparkles className="w-6 h-6 text-blue-500" />
          <h2 className="text-2xl font-bold">Shadcn Block Generator</h2>
        </div>
        <p className="text-muted-foreground">
          Generate high-quality blocks using shadcn/ui components with AI assistance
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Configuration Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="w-5 h-5" />
              Block Configuration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Description */}
            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Describe the block you want to generate..."
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
              />
            </div>

            {/* Block Type */}
            <div className="space-y-2">
              <Label>Block Type</Label>
              <Select value={formData.blockType} onValueChange={(value) => setFormData(prev => ({ ...prev, blockType: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {blockTypes.map(type => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Design Style */}
            <div className="space-y-2">
              <Label>Design Style</Label>
              <Select value={formData.designStyle} onValueChange={(value) => setFormData(prev => ({ ...prev, designStyle: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {designStyles.map(style => (
                    <SelectItem key={style.value} value={style.value}>
                      {style.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Complexity */}
            <div className="space-y-2">
              <Label>Complexity</Label>
              <Select value={formData.complexity} onValueChange={(value) => setFormData(prev => ({ ...prev, complexity: value }))}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {complexityLevels.map(level => (
                    <SelectItem key={level.value} value={level.value}>
                      {level.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            {/* Components Selection */}
            <div className="space-y-3">
              <Label>Shadcn Components</Label>
              <div className="grid grid-cols-2 gap-2 max-h-32 overflow-y-auto">
                {availableComponents.map(component => (
                  <div key={component} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id={component}
                      checked={formData.components.includes(component)}
                      onChange={() => handleComponentToggle(component)}
                      className="rounded"
                    />
                    <Label htmlFor={component} className="text-sm">
                      {component}
                    </Label>
                  </div>
                ))}
              </div>
            </div>

            <Separator />

            {/* Options */}
            <div className="space-y-4">
              <Label>Options</Label>
              
              <div className="flex items-center justify-between">
                <Label htmlFor="responsive" className="text-sm">Responsive Design</Label>
                <Switch
                  id="responsive"
                  checked={formData.responsive}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, responsive: checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="accessibility" className="text-sm">Accessibility Features</Label>
                <Switch
                  id="accessibility"
                  checked={formData.accessibility}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, accessibility: checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="theme" className="text-sm">Theme Support</Label>
                <Switch
                  id="theme"
                  checked={formData.themeSupport}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, themeSupport: checked }))}
                />
              </div>

              <div className="flex items-center justify-between">
                <Label htmlFor="animations" className="text-sm">Animations</Label>
                <Switch
                  id="animations"
                  checked={formData.animations}
                  onCheckedChange={(checked) => setFormData(prev => ({ ...prev, animations: checked }))}
                />
              </div>
            </div>

            {/* Generate Button */}
            <Button 
              onClick={handleGenerate} 
              disabled={isGenerating || !formData.description.trim()}
              className="w-full"
              size="lg"
            >
              {isGenerating ? (
                <>
                  <Zap className="w-4 h-4 mr-2 animate-spin" />
                  Generating...
                </>
              ) : (
                <>
                  <Sparkles className="w-4 h-4 mr-2" />
                  Generate Block
                </>
              )}
            </Button>
          </CardContent>
        </Card>

        {/* Results Panel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="w-5 h-5" />
              Generated Block
            </CardTitle>
          </CardHeader>
          <CardContent>
            {generatedBlock ? (
              <Tabs defaultValue="preview" className="w-full">
                <TabsList className="grid w-full grid-cols-3">
                  <TabsTrigger value="preview">Preview</TabsTrigger>
                  <TabsTrigger value="code">Code</TabsTrigger>
                  <TabsTrigger value="properties">Properties</TabsTrigger>
                </TabsList>

                <TabsContent value="preview" className="space-y-4">
                  <div className="border rounded-lg p-4 bg-muted/50">
                    <div className="text-center space-y-4">
                      <Badge variant="secondary">{generatedBlock.block.name}</Badge>
                      <h3 className="text-xl font-semibold">Preview Component</h3>
                      <p className="text-muted-foreground">
                        {generatedBlock.block.description}
                      </p>
                      <div className="flex gap-2 justify-center">
                        {generatedBlock.block.components.map((comp: string) => (
                          <Badge key={comp} variant="outline">{comp}</Badge>
                        ))}
                      </div>
                    </div>
                  </div>

                  {generatedBlock.recommendations && (
                    <div className="space-y-2">
                      <Label className="flex items-center gap-2">
                        <Info className="w-4 h-4" />
                        Recommendations
                      </Label>
                      <div className="space-y-1">
                        {generatedBlock.recommendations.map((rec: string, index: number) => (
                          <div key={index} className="flex items-start gap-2 text-sm text-muted-foreground">
                            <CheckCircle className="w-3 h-3 mt-0.5 text-green-500" />
                            {rec}
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </TabsContent>

                <TabsContent value="code">
                  <ScrollArea className="h-64 w-full rounded border">
                    <pre className="p-4 text-xs">
                      <code>{generatedBlock.block.jsx}</code>
                    </pre>
                  </ScrollArea>
                </TabsContent>

                <TabsContent value="properties">
                  <div className="space-y-2">
                    <Label>Generated Properties</Label>
                    <ScrollArea className="h-64 w-full rounded border">
                      <div className="p-4 space-y-2">
                        {Object.entries(generatedBlock.block.properties).map(([key, value]: [string, any]) => (
                          <div key={key} className="flex justify-between items-center text-sm">
                            <span className="font-medium">{key}</span>
                            <Badge variant="outline">{value.type}</Badge>
                          </div>
                        ))}
                      </div>
                    </ScrollArea>
                  </div>
                </TabsContent>
              </Tabs>
            ) : (
              <div className="text-center py-12 text-muted-foreground">
                <Layers className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <p>Configure your block and click "Generate Block" to see results</p>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  )
}

// Helper functions for mock data
function generateMockJSX(formData: any): string {
  return `import { ${formData.components.join(', ')} } from '@/components/ui'
import { cn } from '@/lib/utils'

interface ${formData.blockType.charAt(0).toUpperCase() + formData.blockType.slice(1)}BlockProps {
  className?: string
}

export function ${formData.blockType.charAt(0).toUpperCase() + formData.blockType.slice(1)}Block({ 
  className 
}: ${formData.blockType.charAt(0).toUpperCase() + formData.blockType.slice(1)}BlockProps) {
  return (
    <div className={cn("p-6 rounded-lg", className)}>
      {/* Generated ${formData.blockType} block using ${formData.components.join(', ')} */}
      {/* ${formData.description} */}
    </div>
  )
}`
}

function generateMockProperties(formData: any) {
  const properties: Record<string, any> = {}
  
  formData.components.forEach((component: string) => {
    properties[`${component.toLowerCase()}_variant`] = {
      type: 'select',
      label: `${component} Variant`,
      options: ['default', 'secondary', 'outline']
    }
  })
  
  return properties
}

function generateMockVariants(formData: any) {
  return [
    { name: 'default', description: 'Default variant' },
    { name: 'compact', description: 'Compact spacing' },
    { name: 'featured', description: 'Featured highlight' }
  ]
}
