"use client";

import React from "react";
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ChatMessages,
  ChatInput,
  ChatStatus,
  ChatError,
  ChatTools,
  useAIChat,
  useChatTools,
  ToolDefinition,
} from "@/components/ui/ai-chat";
import { useEditorStore } from "../stores/editor-store";
import { GeneratedComponent } from "../types";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import {
  Sparkles,
  Play,
  Copy,
  Code,
  Palette,
  MessageSquare,
  Wrench as Tool,
} from "lucide-react";

// Custom tools for the AI Visual Editor
const visualEditorTools: ToolDefinition[] = [
  {
    name: "generateComponent",
    description: "Generate a React component based on user requirements",
    parameters: {
      type: "object",
      properties: {
        name: {
          type: "string",
          description: "Component name (PascalCase)",
        },
        description: {
          type: "string",
          description: "Brief description of the component",
        },
        category: {
          type: "string",
          enum: ["layout", "ui", "form", "display", "navigation"],
          description: "Component category",
        },
        requirements: {
          type: "string",
          description: "Detailed requirements for the component",
        },
      },
      required: ["name", "description", "category", "requirements"],
    },
    execute: async (args) => {
      // This would typically call your component generation API
      try {
        const response = await fetch(
          "/api/ai-visual-editor/generate-component",
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(args),
          }
        );

        const result = await response.json();
        return result;
      } catch (error) {
        return {
          success: false,
          error: "Failed to generate component",
        };
      }
    },
  },
  {
    name: "analyzeComponent",
    description: "Analyze an existing component for improvements",
    parameters: {
      type: "object",
      properties: {
        componentCode: {
          type: "string",
          description: "The component code to analyze",
        },
        focusArea: {
          type: "string",
          enum: ["performance", "accessibility", "design", "functionality"],
          description: "What aspect to focus the analysis on",
        },
      },
      required: ["componentCode"],
    },
    execute: async (args) => {
      try {
        const response = await fetch(
          "/api/ai-visual-editor/analyze-component",
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(args),
          }
        );

        const result = await response.json();
        return result;
      } catch (error) {
        return {
          success: false,
          error: "Failed to analyze component",
        };
      }
    },
  },
  {
    name: "optimizeComponent",
    description: "Optimize a component for better performance or code quality",
    parameters: {
      type: "object",
      properties: {
        componentCode: {
          type: "string",
          description: "The component code to optimize",
        },
        optimizationType: {
          type: "string",
          enum: ["performance", "bundle-size", "readability", "accessibility"],
          description: "Type of optimization to apply",
        },
      },
      required: ["componentCode", "optimizationType"],
    },
    execute: async (args) => {
      try {
        const response = await fetch(
          "/api/ai-visual-editor/optimize-component",
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(args),
          }
        );

        const result = await response.json();
        return result;
      } catch (error) {
        return {
          success: false,
          error: "Failed to optimize component",
        };
      }
    },
  },
];

export function AiChatPanel() {
  const { addComponent, setGenerating, setAIResponding } = useEditorStore();
  const [selectedModel, setSelectedModel] = React.useState("gpt-4o");

  const {
    messages,
    input,
    status,
    error,
    isLoading,
    handleInputChange,
    sendMessage,
    clearChat,
    regenerateLastMessage,
    stopGeneration,
    retryMessage,
  } = useAIChat({
    api: "/api/ai-visual-editor/chat",
    onResponse: () => {
      setAIResponding(true);
    },
    onFinish: () => {
      setAIResponding(false);
      setGenerating(false);
    },
    onError: (error) => {
      console.error("Chat error:", error);
      setAIResponding(false);
      setGenerating(false);
      toast.error("Failed to communicate with AI. Please try again.");
    },
  });

  const { executeTool, activeCalls } = useChatTools({
    tools: visualEditorTools,
    onToolCall: (toolCall) => {
      console.log("Tool called:", toolCall);
      setGenerating(true);
    },
    onToolResult: (toolCall, result) => {
      console.log("Tool result:", toolCall.name, result);

      // Handle component generation results
      if (
        toolCall.name === "generateComponent" &&
        result.success &&
        result.component
      ) {
        handleComponentGenerated(result.component);
      }

      setGenerating(false);
    },
    onToolError: (toolCall, error) => {
      console.error("Tool error:", toolCall.name, error);
      toast.error(`Tool ${toolCall.name} failed: ${error.message}`);
      setGenerating(false);
    },
  });

  const handleComponentGenerated = (component: GeneratedComponent) => {
    addComponent(component);
    toast.success(`Generated ${component.name} component successfully!`);
  };

  const handleSubmit = (content: string) => {
    if (!content.trim()) return;

    setGenerating(true);
    sendMessage(content);
  };

  // Custom tool result renderer for visual editor tools
  const renderCustomToolResult = (toolCall: any, result: any) => {
    switch (toolCall.name) {
      case "generateComponent":
        if (result.success && result.component) {
          return (
            <Card className="p-4 bg-green-50 border-green-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Sparkles className="w-4 h-4 text-green-600" />
                  <span className="text-sm font-medium text-green-800">
                    Component Generated: {result.component.name}
                  </span>
                </div>
                <div className="flex space-x-2">
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => handleComponentGenerated(result.component)}
                    className="border-green-300 text-green-700 hover:bg-green-100"
                  >
                    <Play className="w-3 h-3 mr-1" />
                    Add to Editor
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    onClick={() => {
                      navigator.clipboard.writeText(result.component.jsx);
                      toast.success("Component code copied to clipboard!");
                    }}
                    className="border-green-300 text-green-700 hover:bg-green-100"
                  >
                    <Copy className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              <div className="text-xs text-green-700 mb-2">
                {result.component.description}
              </div>
              <Badge variant="secondary" className="text-xs">
                {result.component.category}
              </Badge>
            </Card>
          );
        } else {
          return (
            <Card className="p-4 bg-red-50 border-red-200">
              <div className="flex items-center space-x-2 mb-2">
                <span className="text-sm font-medium text-red-800">
                  Generation Failed
                </span>
              </div>
              <div className="text-xs text-red-700">
                {result.error || result.message || "Unknown error occurred"}
              </div>
            </Card>
          );
        }

      case "analyzeComponent":
        return (
          <Card className="p-4 bg-blue-50 border-blue-200">
            <div className="flex items-center space-x-2 mb-2">
              <Code className="w-4 h-4 text-blue-600" />
              <span className="text-sm font-medium text-blue-800">
                Component Analysis
              </span>
            </div>
            <div className="text-xs text-blue-700">
              {result.analysis || "Analysis completed"}
            </div>
          </Card>
        );

      case "optimizeComponent":
        if (result.success) {
          return (
            <Card className="p-4 bg-purple-50 border-purple-200">
              <div className="flex items-center justify-between mb-2">
                <div className="flex items-center space-x-2">
                  <Palette className="w-4 h-4 text-purple-600" />
                  <span className="text-sm font-medium text-purple-800">
                    Component Optimized
                  </span>
                </div>
                <Button
                  size="sm"
                  variant="outline"
                  onClick={() => {
                    navigator.clipboard.writeText(result.optimizedCode);
                    toast.success("Optimized code copied to clipboard!");
                  }}
                  className="border-purple-300 text-purple-700 hover:bg-purple-100"
                >
                  <Copy className="w-3 h-3" />
                </Button>
              </div>
              <div className="text-xs text-purple-700 mb-2">
                {result.improvements}
              </div>
              <div className="text-xs text-purple-600">
                Changes: {result.changes?.join(", ")}
              </div>
            </Card>
          );
        }
        break;

      default:
        return null;
    }
  };

  return (
    <ChatContainer className="h-full" variant="minimal">
      <ChatHeader
        title="AI Visual Editor"
        subtitle="Describe what you want to design"
        avatar={<MessageSquare className="w-5 h-5 text-blue-600" />}
        status={status}
        onClear={clearChat}
      />

      {/* Welcome Message */}
      {messages.length === 0 && (
        <div className="p-6 text-center text-gray-500">
          <Sparkles className="w-12 h-12 mx-auto mb-4 text-gray-300" />
          <div className="text-lg font-medium mb-2">Start Creating</div>
          <div className="text-sm">
            Ask me to create components like:
            <ul className="mt-2 space-y-1 text-left max-w-xs mx-auto">
              <li>• "Create a hero section with a title and CTA button"</li>
              <li>• "Build a pricing card component"</li>
              <li>• "Generate a contact form with validation"</li>
              <li>• "Analyze this component for performance issues"</li>
              <li>• "Optimize this code for better accessibility"</li>
            </ul>
          </div>
        </div>
      )}

      {messages.length > 0 && (
        <ChatMessages
          messages={messages}
          isLoading={isLoading}
          customToolRenderer={renderCustomToolResult}
        />
      )}

      {/* Tool Calls Display */}
      {activeCalls.length > 0 && (
        <div className="px-4 py-2 border-t">
          <ChatTools
            toolInvocations={activeCalls}
            customRenderer={renderCustomToolResult}
          />
        </div>
      )}

      {error && <ChatError error={error} onRetry={retryMessage} />}

      <ChatStatus
        status={status}
        error={error}
        onRetry={retryMessage}
        onStop={stopGeneration}
      />

      <ChatInput
        value={input}
        onChange={handleInputChange}
        onSubmit={handleSubmit}
        disabled={isLoading}
        placeholder="Create a hero section with..."
        selectedModel={selectedModel}
        onModelChange={setSelectedModel}
        showModelSelector={true}
        allowAttachments={true}
        allowVoiceInput={true}
        className="border-t"
      />
    </ChatContainer>
  );
}
