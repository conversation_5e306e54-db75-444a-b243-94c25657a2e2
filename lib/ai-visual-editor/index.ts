// Main components
export { AIVisualEditor } from './components/ai-visual-editor'
export { AIVisualEditorLayout } from './components/ai-visual-editor-layout'
export { AiChatPanel } from './components/ai-chat-panel'
export { PropertiesPanel } from './components/properties-panel'
export { LivePreview } from './components/live-preview'
export { ComponentTree } from './components/component-tree'
export { DynamicComponentRenderer } from './components/dynamic-component-renderer'
export { NextJSLayoutGenerator } from './components/nextjs-layout-generator'
export { ComponentLibrary } from './components/component-library'

// Store and hooks
export { useEditorStore, useSelectedComponent, useSelectedComponentProperties, useComponentById, useComponentProperties } from './stores/editor-store'

// Types
export type {
  GeneratedComponent,
  ComponentPropertiesConfig,
  ComponentAnalysis,
  AIToolResult,
  EditorState,
  EditorActions,
  EditorStore,
  ComponentGenerationParams,
  PropertiesAnalysisParams,
  FieldGenerationResult
} from './types'

// Next.js Types
export type {
  NextJSLayout,
  NextJSPage,
  NextJSProject,
  LayoutGenerationParams,
  PageGenerationParams,
  NextJSMetadata,
  GeneratedNextJSStructure
} from './types/nextjs-types'

// Utilities
export { 
  analyzeComponentStructure, 
  extractPropsFromComponent, 
  generateComponentId, 
  validateComponentCode, 
  sanitizeComponentCode, 
  extractImportsFromComponent 
} from './utils/component-analyzer'

export {
  generateAppearanceFields,
  generateContentFields,
  generateBehaviorFields,
  generateDataFields,
  generateLayoutFields,
  generatePropertiesSchema,
  generateDefaultValues
} from './utils/properties-generator'

// Next.js Services
export { NextJSGenerator } from './services/nextjs-generator'
export { componentPersistence } from './services/component-persistence'

// Shadcn Block Generation Services
export { shadcnBlockService, ShadcnBlockService } from './services/shadcn-block-service'
export type { ShadcnBlockGenerationRequest, ShadcnBlockGenerationResult } from './services/shadcn-block-service'

// Shadcn Tools
export {
  generateShadcnBlockTool,
  analyzeShadcnComponentTool,
  generateShadcnPropertiesTool,
  optimizeShadcnBlockTool,
  generateShadcnVariantsTool
} from './tools/shadcn-block-generation-tools'

// Shadcn Templates
export {
  SHADCN_BLOCK_TEMPLATES,
  heroTemplates,
  featureTemplates,
  pricingTemplates,
  getTemplatesByCategory,
  getTemplateById,
  getTemplatesByComponents,
  searchTemplates
} from './templates/shadcn-block-templates'
export type { ShadcnBlockTemplate } from './templates/shadcn-block-templates'

// Shadcn Properties Generator
export {
  generateShadcnComponentProperties,
  generateEnhancedShadcnProperties,
  generateShadcnPropertyPresets
} from './utils/shadcn-properties-generator'

// Intelligent Block Generation System
export { intelligentBlockService, IntelligentBlockService } from './services/intelligent-block-service'
export type {
  IntelligentBlockRequest,
  IntelligentBlockResult,
  CodebaseAnalysis,
  LearnedPatterns
} from './services/intelligent-block-service'

// Codebase Analyzer
export { codebaseAnalyzer, CodebaseAnalyzer } from './services/codebase-analyzer'
export type {
  CodebaseComponent,
  ImportInfo,
  ExportInfo,
  PropDefinition,
  UsagePattern,
  StylePattern,
  CompositionPattern,
  ComponentRelationship,
  ComponentCategory
} from './services/codebase-analyzer'

// Intelligent Generation Tools
export {
  generateIntelligentBlockTool,
  analyzeCodebasePatternsToolTool,
  generateContextAwareComponentTool
} from './tools/intelligent-block-generator'

// Demo Components
export { IntelligentBlockGeneratorDemo } from './components/intelligent-block-generator-demo'

// Performance Hooks
export {
  usePerformanceMonitor,
  useComponentPerformance,
  useDebouncedUpdate,
  useVirtualization,
  useLazyComponent,
  PerformanceUtils
} from './hooks/use-performance-monitor'
