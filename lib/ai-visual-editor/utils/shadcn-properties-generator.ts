import { ComponentPropertiesConfig } from '../types'

// Shadcn component property mappings
const SHADCN_COMPONENT_PROPS = {
  Button: {
    variant: ['default', 'destructive', 'outline', 'secondary', 'ghost', 'link'],
    size: ['default', 'sm', 'lg', 'icon'],
    disabled: 'boolean',
    asChild: 'boolean'
  },
  Card: {
    className: 'string'
  },
  Input: {
    type: ['text', 'email', 'password', 'number', 'tel', 'url'],
    placeholder: 'string',
    disabled: 'boolean',
    required: 'boolean'
  },
  Badge: {
    variant: ['default', 'secondary', 'destructive', 'outline']
  },
  Alert: {
    variant: ['default', 'destructive']
  },
  Avatar: {
    className: 'string'
  },
  Checkbox: {
    checked: 'boolean',
    disabled: 'boolean',
    required: 'boolean'
  },
  Select: {
    disabled: 'boolean',
    required: 'boolean'
  },
  Switch: {
    checked: 'boolean',
    disabled: 'boolean'
  },
  Tabs: {
    defaultValue: 'string',
    orientation: ['horizontal', 'vertical']
  },
  Dialog: {
    open: 'boolean'
  },
  Sheet: {
    side: ['top', 'right', 'bottom', 'left']
  },
  Accordion: {
    type: ['single', 'multiple'],
    collapsible: 'boolean'
  }
} as const

// Generate properties for shadcn components
export function generateShadcnComponentProperties(components: string[]): ComponentPropertiesConfig {
  const properties: ComponentPropertiesConfig = {
    appearance: {
      title: 'Appearance',
      fields: {}
    },
    layout: {
      title: 'Layout',
      fields: {}
    },
    content: {
      title: 'Content',
      fields: {}
    },
    behavior: {
      title: 'Behavior',
      fields: {}
    }
  }

  components.forEach(component => {
    const componentProps = SHADCN_COMPONENT_PROPS[component as keyof typeof SHADCN_COMPONENT_PROPS]
    if (componentProps) {
      addComponentPropertiesToConfig(properties, component, componentProps)
    }
  })

  return properties
}

function addComponentPropertiesToConfig(
  config: ComponentPropertiesConfig,
  componentName: string,
  componentProps: any
) {
  const prefix = componentName.toLowerCase()

  Object.entries(componentProps).forEach(([propName, propConfig]) => {
    const fieldKey = `${prefix}_${propName}`
    const field = createPropertyField(propName, propConfig, componentName)
    
    // Categorize properties
    if (isAppearanceProperty(propName)) {
      config.appearance.fields[fieldKey] = field
    } else if (isLayoutProperty(propName)) {
      config.layout.fields[fieldKey] = field
    } else if (isContentProperty(propName)) {
      config.content.fields[fieldKey] = field
    } else {
      config.behavior.fields[fieldKey] = field
    }
  })
}

function createPropertyField(propName: string, propConfig: any, componentName: string) {
  if (Array.isArray(propConfig)) {
    // Enum/select field
    return {
      type: 'select' as const,
      label: `${componentName} ${formatLabel(propName)}`,
      options: propConfig.map(value => ({
        label: formatLabel(value),
        value
      })),
      defaultValue: propConfig[0],
      description: `Select ${propName} for ${componentName} component`
    }
  }

  if (propConfig === 'boolean') {
    return {
      type: 'switch' as const,
      label: `${componentName} ${formatLabel(propName)}`,
      defaultValue: false,
      description: `Toggle ${propName} for ${componentName} component`
    }
  }

  if (propConfig === 'string') {
    return {
      type: 'text' as const,
      label: `${componentName} ${formatLabel(propName)}`,
      defaultValue: '',
      placeholder: `Enter ${propName}...`,
      description: `Set ${propName} for ${componentName} component`
    }
  }

  if (propConfig === 'number') {
    return {
      type: 'number' as const,
      label: `${componentName} ${formatLabel(propName)}`,
      defaultValue: 0,
      min: 0,
      description: `Set ${propName} for ${componentName} component`
    }
  }

  // Default to text field
  return {
    type: 'text' as const,
    label: `${componentName} ${formatLabel(propName)}`,
    defaultValue: '',
    description: `Configure ${propName} for ${componentName} component`
  }
}

function formatLabel(text: string): string {
  return text
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim()
}

function isAppearanceProperty(propName: string): boolean {
  const appearanceProps = ['variant', 'size', 'color', 'theme', 'style']
  return appearanceProps.some(prop => propName.toLowerCase().includes(prop))
}

function isLayoutProperty(propName: string): boolean {
  const layoutProps = ['orientation', 'side', 'position', 'align', 'justify']
  return layoutProps.some(prop => propName.toLowerCase().includes(prop))
}

function isContentProperty(propName: string): boolean {
  const contentProps = ['placeholder', 'text', 'content', 'value', 'defaultValue']
  return contentProps.some(prop => propName.toLowerCase().includes(prop))
}

// Generate enhanced properties with shadcn-specific features
export function generateEnhancedShadcnProperties(
  components: string[],
  blockType: string,
  options: {
    includeThemeSupport?: boolean
    includeResponsive?: boolean
    includeAnimations?: boolean
    includeAccessibility?: boolean
  } = {}
): ComponentPropertiesConfig {
  const baseProperties = generateShadcnComponentProperties(components)

  // Add theme support
  if (options.includeThemeSupport) {
    baseProperties.appearance.fields.theme = {
      type: 'select',
      label: 'Theme',
      options: [
        { label: 'Light', value: 'light' },
        { label: 'Dark', value: 'dark' },
        { label: 'System', value: 'system' }
      ],
      defaultValue: 'system',
      description: 'Select the theme for this block'
    }

    baseProperties.appearance.fields.colorScheme = {
      type: 'select',
      label: 'Color Scheme',
      options: [
        { label: 'Blue', value: 'blue' },
        { label: 'Gray', value: 'gray' },
        { label: 'Green', value: 'green' },
        { label: 'Red', value: 'red' },
        { label: 'Purple', value: 'purple' },
        { label: 'Orange', value: 'orange' }
      ],
      defaultValue: 'blue',
      description: 'Select the color scheme'
    }
  }

  // Add responsive properties
  if (options.includeResponsive) {
    baseProperties.layout.fields.responsiveColumns = {
      type: 'object',
      label: 'Responsive Columns',
      properties: {
        mobile: { type: 'number', defaultValue: 1, min: 1, max: 4 },
        tablet: { type: 'number', defaultValue: 2, min: 1, max: 6 },
        desktop: { type: 'number', defaultValue: 3, min: 1, max: 12 }
      },
      description: 'Configure columns for different screen sizes'
    }

    baseProperties.layout.fields.spacing = {
      type: 'select',
      label: 'Spacing',
      options: [
        { label: 'Compact', value: 'compact' },
        { label: 'Normal', value: 'normal' },
        { label: 'Spacious', value: 'spacious' }
      ],
      defaultValue: 'normal',
      description: 'Control spacing between elements'
    }
  }

  // Add animation properties
  if (options.includeAnimations) {
    baseProperties.behavior.fields.animations = {
      type: 'switch',
      label: 'Enable Animations',
      defaultValue: true,
      description: 'Enable smooth animations and transitions'
    }

    baseProperties.behavior.fields.animationDuration = {
      type: 'select',
      label: 'Animation Duration',
      options: [
        { label: 'Fast (150ms)', value: '150' },
        { label: 'Normal (300ms)', value: '300' },
        { label: 'Slow (500ms)', value: '500' }
      ],
      defaultValue: '300',
      description: 'Control animation speed'
    }
  }

  // Add accessibility properties
  if (options.includeAccessibility) {
    baseProperties.behavior.fields.ariaLabel = {
      type: 'text',
      label: 'ARIA Label',
      defaultValue: '',
      placeholder: 'Descriptive label for screen readers',
      description: 'Provide accessible label for screen readers'
    }

    baseProperties.behavior.fields.keyboardNavigation = {
      type: 'switch',
      label: 'Keyboard Navigation',
      defaultValue: true,
      description: 'Enable keyboard navigation support'
    }

    baseProperties.behavior.fields.highContrast = {
      type: 'switch',
      label: 'High Contrast Mode',
      defaultValue: false,
      description: 'Enable high contrast for better accessibility'
    }
  }

  return baseProperties
}

// Generate property presets for common configurations
export function generateShadcnPropertyPresets(components: string[], blockType: string) {
  const presets = [
    {
      name: 'Default',
      description: 'Standard configuration with balanced settings',
      values: generateDefaultPresetValues(components, blockType)
    },
    {
      name: 'Minimal',
      description: 'Clean and minimal appearance',
      values: generateMinimalPresetValues(components, blockType)
    },
    {
      name: 'Bold',
      description: 'High-impact, attention-grabbing design',
      values: generateBoldPresetValues(components, blockType)
    },
    {
      name: 'Accessible',
      description: 'Optimized for accessibility and screen readers',
      values: generateAccessiblePresetValues(components, blockType)
    }
  ]

  return presets
}

function generateDefaultPresetValues(components: string[], blockType: string) {
  const values: Record<string, any> = {}
  
  components.forEach(component => {
    const prefix = component.toLowerCase()
    const componentProps = SHADCN_COMPONENT_PROPS[component as keyof typeof SHADCN_COMPONENT_PROPS]
    
    if (componentProps) {
      Object.entries(componentProps).forEach(([propName, propConfig]) => {
        const fieldKey = `${prefix}_${propName}`
        
        if (Array.isArray(propConfig)) {
          values[fieldKey] = propConfig[0] // First option as default
        } else if (propConfig === 'boolean') {
          values[fieldKey] = false
        } else {
          values[fieldKey] = ''
        }
      })
    }
  })
  
  return values
}

function generateMinimalPresetValues(components: string[], blockType: string) {
  const values = generateDefaultPresetValues(components, blockType)
  
  // Override with minimal-specific values
  Object.keys(values).forEach(key => {
    if (key.includes('variant')) {
      values[key] = 'outline'
    }
    if (key.includes('size')) {
      values[key] = 'sm'
    }
  })
  
  return values
}

function generateBoldPresetValues(components: string[], blockType: string) {
  const values = generateDefaultPresetValues(components, blockType)
  
  // Override with bold-specific values
  Object.keys(values).forEach(key => {
    if (key.includes('variant')) {
      values[key] = 'default'
    }
    if (key.includes('size')) {
      values[key] = 'lg'
    }
  })
  
  return values
}

function generateAccessiblePresetValues(components: string[], blockType: string) {
  const values = generateDefaultPresetValues(components, blockType)
  
  // Add accessibility-focused values
  values.keyboardNavigation = true
  values.highContrast = true
  values.ariaLabel = `${blockType} block`
  
  return values
}
