import { 
  ShadcnBlockTemplate, 
  getTemplatesByCategory, 
  getTemplateById, 
  getTemplatesByComponents,
  searchTemplates 
} from '../templates/shadcn-block-templates'
import { generateEnhancedShadcnProperties } from '../utils/shadcn-properties-generator'
import { GeneratedComponent } from '../types'

export interface ShadcnBlockGenerationRequest {
  description: string
  blockType: 'hero' | 'feature' | 'testimonial' | 'pricing' | 'contact' | 'product' | 'content' | 'navigation' | 'footer'
  components?: string[]
  designStyle?: 'modern' | 'minimal' | 'bold' | 'elegant' | 'playful' | 'professional'
  complexity?: 'simple' | 'moderate' | 'complex'
  requirements?: {
    responsive?: boolean
    accessibility?: boolean
    themeSupport?: boolean
    animations?: boolean
  }
  customization?: {
    colorScheme?: string
    spacing?: 'compact' | 'normal' | 'spacious'
    borderRadius?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  }
}

export interface ShadcnBlockGenerationResult {
  success: boolean
  block?: GeneratedComponent
  template?: ShadcnBlockTemplate
  alternatives?: ShadcnBlockTemplate[]
  recommendations?: string[]
  error?: string
}

export class ShadcnBlockService {
  /**
   * Generate a shadcn block using AI and templates
   */
  async generateBlock(request: ShadcnBlockGenerationRequest): Promise<ShadcnBlockGenerationResult> {
    try {
      // 1. Find suitable templates
      const templates = this.findSuitableTemplates(request)
      
      // 2. Select best template or create custom
      const selectedTemplate = this.selectOptimalTemplate(templates, request)
      
      // 3. Generate or customize the block
      const block = selectedTemplate 
        ? await this.customizeTemplate(selectedTemplate, request)
        : await this.generateCustomBlock(request)
      
      // 4. Generate properties configuration
      const properties = this.generatePropertiesConfig(block, request)
      
      // 5. Apply customizations
      const finalBlock = this.applyCustomizations(block, request, properties)
      
      return {
        success: true,
        block: finalBlock,
        template: selectedTemplate,
        alternatives: templates.slice(1, 4), // Up to 3 alternatives
        recommendations: this.generateRecommendations(finalBlock, request)
      }
    } catch (error) {
      console.error('Shadcn block generation error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Block generation failed'
      }
    }
  }

  /**
   * Analyze existing component for shadcn optimization
   */
  async analyzeComponent(componentCode: string, componentName: string): Promise<{
    shadcnComponents: string[]
    optimizations: string[]
    accessibility: { score: number; issues: string[] }
    performance: { score: number; suggestions: string[] }
    designConsistency: { score: number; improvements: string[] }
  }> {
    // Extract shadcn components from code
    const shadcnComponents = this.extractShadcnComponents(componentCode)
    
    // Analyze for optimizations
    const optimizations = this.analyzeOptimizations(componentCode)
    
    // Check accessibility
    const accessibility = this.analyzeAccessibility(componentCode)
    
    // Performance analysis
    const performance = this.analyzePerformance(componentCode)
    
    // Design consistency check
    const designConsistency = this.analyzeDesignConsistency(componentCode, shadcnComponents)
    
    return {
      shadcnComponents,
      optimizations,
      accessibility,
      performance,
      designConsistency
    }
  }

  /**
   * Generate variants of an existing block
   */
  async generateVariants(baseBlock: GeneratedComponent, variantTypes: string[]): Promise<GeneratedComponent[]> {
    const variants: GeneratedComponent[] = []
    
    for (const variantType of variantTypes) {
      const variant = await this.createVariant(baseBlock, variantType)
      if (variant) {
        variants.push(variant)
      }
    }
    
    return variants
  }

  /**
   * Optimize existing block for better performance/accessibility
   */
  async optimizeBlock(block: GeneratedComponent, optimizationType: string): Promise<GeneratedComponent> {
    let optimizedCode = block.code
    const improvements: string[] = []
    
    switch (optimizationType) {
      case 'performance':
        const perfResult = this.optimizeForPerformance(optimizedCode)
        optimizedCode = perfResult.code
        improvements.push(...perfResult.improvements)
        break
        
      case 'accessibility':
        const a11yResult = this.optimizeForAccessibility(optimizedCode)
        optimizedCode = a11yResult.code
        improvements.push(...a11yResult.improvements)
        break
        
      case 'design':
        const designResult = this.optimizeForDesignConsistency(optimizedCode)
        optimizedCode = designResult.code
        improvements.push(...designResult.improvements)
        break
        
      case 'comprehensive':
        // Apply all optimizations
        const allOptimizations = [
          this.optimizeForPerformance(optimizedCode),
          this.optimizeForAccessibility(optimizedCode),
          this.optimizeForDesignConsistency(optimizedCode)
        ]
        
        optimizedCode = allOptimizations.reduce((code, opt) => opt.code, optimizedCode)
        improvements.push(...allOptimizations.flatMap(opt => opt.improvements))
        break
    }
    
    return {
      ...block,
      code: optimizedCode,
      updatedAt: new Date(),
      // Add optimization metadata
      metadata: {
        ...block.metadata,
        optimizations: improvements,
        optimizedAt: new Date().toISOString()
      }
    }
  }

  // Private helper methods
  private findSuitableTemplates(request: ShadcnBlockGenerationRequest): ShadcnBlockTemplate[] {
    let templates: ShadcnBlockTemplate[] = []
    
    // Search by category
    templates = getTemplatesByCategory(request.blockType)
    
    // Filter by components if specified
    if (request.components && request.components.length > 0) {
      const componentTemplates = getTemplatesByComponents(request.components)
      templates = templates.filter(t => componentTemplates.includes(t))
    }
    
    // Filter by complexity
    if (request.complexity) {
      templates = templates.filter(t => t.complexity === request.complexity)
    }
    
    // Search by description keywords
    if (request.description) {
      const searchResults = searchTemplates(request.description)
      templates = [...templates, ...searchResults].filter((t, i, arr) => 
        arr.findIndex(template => template.id === t.id) === i
      )
    }
    
    return templates
  }

  private selectOptimalTemplate(templates: ShadcnBlockTemplate[], request: ShadcnBlockGenerationRequest): ShadcnBlockTemplate | null {
    if (templates.length === 0) return null
    
    // Score templates based on request criteria
    const scoredTemplates = templates.map(template => ({
      template,
      score: this.scoreTemplate(template, request)
    }))
    
    // Sort by score and return best match
    scoredTemplates.sort((a, b) => b.score - a.score)
    
    return scoredTemplates[0].score > 0.5 ? scoredTemplates[0].template : null
  }

  private scoreTemplate(template: ShadcnBlockTemplate, request: ShadcnBlockGenerationRequest): number {
    let score = 0
    
    // Category match
    if (template.category === request.blockType) score += 0.4
    
    // Component match
    if (request.components) {
      const matchingComponents = template.components.filter(c => request.components!.includes(c))
      score += (matchingComponents.length / Math.max(template.components.length, request.components.length)) * 0.3
    }
    
    // Complexity match
    if (template.complexity === request.complexity) score += 0.2
    
    // Use case relevance
    const descriptionWords = request.description.toLowerCase().split(' ')
    const relevantUseCases = template.useCases.filter(useCase => 
      descriptionWords.some(word => useCase.toLowerCase().includes(word))
    )
    score += (relevantUseCases.length / template.useCases.length) * 0.1
    
    return score
  }

  private async customizeTemplate(template: ShadcnBlockTemplate, request: ShadcnBlockGenerationRequest): Promise<GeneratedComponent> {
    // Customize template based on request
    const customizedProps = { ...template.props }
    
    // Apply design style customizations
    if (request.designStyle) {
      customizedProps.designStyle = request.designStyle
    }
    
    // Apply custom styling
    if (request.customization) {
      Object.assign(customizedProps, request.customization)
    }
    
    return {
      id: `shadcn-${template.id}-${Date.now()}`,
      name: template.name,
      description: template.description,
      code: template.jsx,
      props: customizedProps,
      defaultValues: customizedProps,
      propertiesConfig: {},
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        templateId: template.id,
        shadcnComponents: template.components,
        category: template.category,
        complexity: template.complexity
      }
    }
  }

  private async generateCustomBlock(request: ShadcnBlockGenerationRequest): Promise<GeneratedComponent> {
    // Generate completely custom block when no suitable template exists
    // This would integrate with the AI generation tools
    
    const blockId = `custom-shadcn-${Date.now()}`
    
    return {
      id: blockId,
      name: `Custom ${request.blockType} Block`,
      description: request.description,
      code: this.generateBasicBlockCode(request),
      props: {},
      defaultValues: {},
      propertiesConfig: {},
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        isCustomGenerated: true,
        blockType: request.blockType,
        designStyle: request.designStyle,
        complexity: request.complexity
      }
    }
  }

  private generatePropertiesConfig(block: GeneratedComponent, request: ShadcnBlockGenerationRequest) {
    const components = block.metadata?.shadcnComponents || []
    
    return generateEnhancedShadcnProperties(components, request.blockType, {
      includeThemeSupport: request.requirements?.themeSupport,
      includeResponsive: request.requirements?.responsive,
      includeAnimations: request.requirements?.animations,
      includeAccessibility: request.requirements?.accessibility
    })
  }

  private applyCustomizations(
    block: GeneratedComponent, 
    request: ShadcnBlockGenerationRequest, 
    properties: any
  ): GeneratedComponent {
    return {
      ...block,
      propertiesConfig: properties,
      // Apply any final customizations
      defaultValues: {
        ...block.defaultValues,
        ...request.customization
      }
    }
  }

  private generateRecommendations(block: GeneratedComponent, request: ShadcnBlockGenerationRequest): string[] {
    const recommendations: string[] = []
    
    if (request.requirements?.accessibility && !block.metadata?.hasAccessibilityFeatures) {
      recommendations.push('Consider adding ARIA labels and keyboard navigation support')
    }
    
    if (request.requirements?.responsive && !block.code.includes('responsive')) {
      recommendations.push('Add responsive breakpoints for better mobile experience')
    }
    
    if (block.metadata?.shadcnComponents && block.metadata.shadcnComponents.length > 5) {
      recommendations.push('Consider simplifying component structure for better performance')
    }
    
    return recommendations
  }

  // Placeholder methods for analysis functions
  private extractShadcnComponents(code: string): string[] { return [] }
  private analyzeOptimizations(code: string): string[] { return [] }
  private analyzeAccessibility(code: string) { return { score: 85, issues: [] } }
  private analyzePerformance(code: string) { return { score: 90, suggestions: [] } }
  private analyzeDesignConsistency(code: string, components: string[]) { return { score: 88, improvements: [] } }
  private async createVariant(block: GeneratedComponent, type: string): Promise<GeneratedComponent | null> { return null }
  private optimizeForPerformance(code: string) { return { code, improvements: [] } }
  private optimizeForAccessibility(code: string) { return { code, improvements: [] } }
  private optimizeForDesignConsistency(code: string) { return { code, improvements: [] } }
  private generateBasicBlockCode(request: ShadcnBlockGenerationRequest): string { return '' }
}

// Export singleton instance
export const shadcnBlockService = new ShadcnBlockService()
