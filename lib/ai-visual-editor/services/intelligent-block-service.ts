import { codebaseA<PERSON>yzer, CodebaseComponent } from './codebase-analyzer'
import { GeneratedComponent } from '../types'

export interface IntelligentBlockRequest {
  description: string
  blockType: 'hero' | 'feature' | 'testimonial' | 'pricing' | 'contact' | 'product' | 'content' | 'navigation' | 'footer'
  contextPath?: string
  learnFromCodebase?: boolean
  matchExistingStyle?: boolean
  preferredComponents?: string[]
  complexity?: 'simple' | 'moderate' | 'complex'
  requirements?: {
    responsive?: boolean
    accessibility?: boolean
    themeSupport?: boolean
    animations?: boolean
  }
}

export interface IntelligentBlockResult {
  success: boolean
  block?: GeneratedComponent
  codebaseAnalysis?: CodebaseAnalysis
  styleConsistency?: number
  recommendations?: string[]
  learnedPatterns?: LearnedPatterns
  error?: string
}

export interface CodebaseAnalysis {
  totalComponents: number
  shadcnUsage: Record<string, number>
  commonPatterns: Array<{ pattern: string; frequency: number; examples: string[] }>
  architecturalPatterns: {
    fileStructure: Record<string, number>
    namingConventions: Record<string, number>
    importPatterns: Record<string, number>
  }
  similarComponents: CodebaseComponent[]
}

export interface LearnedPatterns {
  componentComposition: string[]
  stylingApproaches: string[]
  propPatterns: string[]
  namingConventions: string[]
  importStyles: string[]
  codeStructure: string[]
}

export class IntelligentBlockService {
  private analysisCache: Map<string, any> = new Map()
  private componentCache: Map<string, CodebaseComponent[]> = new Map()

  /**
   * Generate a block using intelligent codebase analysis
   */
  async generateIntelligentBlock(request: IntelligentBlockRequest): Promise<IntelligentBlockResult> {
    try {
      // Step 1: Analyze codebase if requested
      let codebaseAnalysis: CodebaseAnalysis | undefined
      let learnedPatterns: LearnedPatterns | undefined

      if (request.learnFromCodebase !== false) {
        codebaseAnalysis = await this.analyzeCodebase(request.contextPath)
        learnedPatterns = this.extractLearnedPatterns(codebaseAnalysis)
      }

      // Step 2: Find similar components for reference
      const similarComponents = codebaseAnalysis 
        ? this.findSimilarComponents(request.blockType, codebaseAnalysis.similarComponents)
        : []

      // Step 3: Generate block using learned patterns
      const block = await this.generateBlockFromPatterns({
        request,
        codebaseAnalysis,
        learnedPatterns,
        similarComponents
      })

      // Step 4: Calculate style consistency
      const styleConsistency = codebaseAnalysis 
        ? this.calculateStyleConsistency(block, codebaseAnalysis.similarComponents)
        : undefined

      // Step 5: Generate recommendations
      const recommendations = this.generateIntelligentRecommendations(
        block,
        codebaseAnalysis,
        learnedPatterns
      )

      return {
        success: true,
        block,
        codebaseAnalysis,
        styleConsistency,
        recommendations,
        learnedPatterns
      }
    } catch (error) {
      console.error('Intelligent block generation error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate intelligent block'
      }
    }
  }

  /**
   * Analyze codebase to understand patterns and architecture
   */
  async analyzeCodebase(contextPath?: string): Promise<CodebaseAnalysis> {
    const cacheKey = `codebase-analysis-${contextPath || 'global'}`
    
    if (this.analysisCache.has(cacheKey)) {
      return this.analysisCache.get(cacheKey)
    }

    // Discover all components
    const components = await codebaseAnalyzer.discoverComponents()
    this.componentCache.set('all', components)

    // Get usage patterns
    const usagePatterns = await codebaseAnalyzer.getComponentUsagePatterns()
    
    // Get architectural patterns
    const architecturalPatterns = await codebaseAnalyzer.getArchitecturalPatterns()

    // Filter components by context if provided
    const contextComponents = contextPath 
      ? this.filterComponentsByContext(components, contextPath)
      : components

    const analysis: CodebaseAnalysis = {
      totalComponents: components.length,
      shadcnUsage: usagePatterns.shadcnUsage,
      commonPatterns: usagePatterns.commonPatterns,
      architecturalPatterns,
      similarComponents: contextComponents
    }

    this.analysisCache.set(cacheKey, analysis)
    return analysis
  }

  /**
   * Extract learned patterns from codebase analysis
   */
  private extractLearnedPatterns(analysis: CodebaseAnalysis): LearnedPatterns {
    const patterns: LearnedPatterns = {
      componentComposition: [],
      stylingApproaches: [],
      propPatterns: [],
      namingConventions: [],
      importStyles: [],
      codeStructure: []
    }

    // Extract component composition patterns
    patterns.componentComposition = analysis.commonPatterns
      .filter(p => p.pattern.includes('composition'))
      .map(p => p.pattern)

    // Extract styling approaches
    patterns.stylingApproaches = analysis.commonPatterns
      .filter(p => p.pattern.includes('styling') || p.pattern.includes('className'))
      .map(p => p.pattern)

    // Extract prop patterns
    patterns.propPatterns = analysis.commonPatterns
      .filter(p => p.pattern.includes('prop'))
      .map(p => p.pattern)

    // Extract naming conventions
    patterns.namingConventions = Object.keys(analysis.architecturalPatterns.namingConventions)

    // Extract import styles
    patterns.importStyles = Object.keys(analysis.architecturalPatterns.importPatterns)

    // Extract code structure patterns
    patterns.codeStructure = Object.keys(analysis.architecturalPatterns.fileStructure)

    return patterns
  }

  /**
   * Generate block based on learned patterns
   */
  private async generateBlockFromPatterns(config: {
    request: IntelligentBlockRequest
    codebaseAnalysis?: CodebaseAnalysis
    learnedPatterns?: LearnedPatterns
    similarComponents: CodebaseComponent[]
  }): Promise<GeneratedComponent> {
    const { request, codebaseAnalysis, learnedPatterns, similarComponents } = config

    // Select components based on codebase usage
    const selectedComponents = this.selectOptimalComponents(
      request.blockType,
      request.preferredComponents,
      codebaseAnalysis?.shadcnUsage
    )

    // Determine code style from similar components
    const codeStyle = this.extractCodeStyle(similarComponents, learnedPatterns)

    // Generate component name following conventions
    const componentName = this.generateComponentName(
      request.blockType,
      request.description,
      codeStyle.namingConvention
    )

    // Generate JSX following patterns
    const jsx = this.generateIntelligentJSX({
      componentName,
      blockType: request.blockType,
      description: request.description,
      components: selectedComponents,
      codeStyle,
      similarComponents,
      complexity: request.complexity || 'moderate',
      requirements: request.requirements
    })

    // Generate properties configuration
    const propertiesConfig = this.generatePropertiesFromPatterns(
      selectedComponents,
      similarComponents,
      learnedPatterns
    )

    // Generate default values
    const defaultValues = this.generateDefaultValues(propertiesConfig, codeStyle)

    return {
      id: `intelligent-${request.blockType}-${Date.now()}`,
      name: componentName,
      description: request.description,
      category: this.mapBlockTypeToCategory(request.blockType),
      jsx,
      props: this.extractPropsFromJSX(jsx),
      propertiesConfig,
      defaultValues,
      createdAt: new Date(),
      updatedAt: new Date(),
      metadata: {
        generationType: 'intelligent',
        learnedFrom: similarComponents.length,
        shadcnComponents: selectedComponents,
        codeStyle,
        complexity: request.complexity || 'moderate'
      }
    }
  }

  /**
   * Select optimal components based on codebase usage
   */
  private selectOptimalComponents(
    blockType: string,
    preferredComponents?: string[],
    shadcnUsage?: Record<string, number>
  ): string[] {
    let components: string[] = []

    // Start with preferred components
    if (preferredComponents) {
      components.push(...preferredComponents)
    }

    // Add most used components from codebase
    if (shadcnUsage) {
      const mostUsed = Object.entries(shadcnUsage)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([comp]) => comp)
      
      components.push(...mostUsed)
    }

    // Add block-type specific components
    const blockSpecific = this.getBlockSpecificComponents(blockType)
    components.push(...blockSpecific)

    // Remove duplicates and limit
    return [...new Set(components)].slice(0, 6)
  }

  /**
   * Extract code style from similar components
   */
  private extractCodeStyle(
    similarComponents: CodebaseComponent[],
    learnedPatterns?: LearnedPatterns
  ) {
    const defaultStyle = {
      namingConvention: 'PascalCase',
      importStyle: 'named',
      exportStyle: 'default',
      indentation: '  ',
      quotesStyle: 'single',
      semicolons: true,
      trailingCommas: true
    }

    if (similarComponents.length === 0) {
      return defaultStyle
    }

    // Analyze patterns from similar components
    const namingPatterns = similarComponents.map(c => this.analyzeNamingPattern(c.name))
    const mostCommonNaming = this.getMostCommon(namingPatterns)

    const importStyles = similarComponents.flatMap(c => c.imports.map(i => i.type))
    const mostCommonImport = this.getMostCommon(importStyles)

    return {
      ...defaultStyle,
      namingConvention: mostCommonNaming || defaultStyle.namingConvention,
      importStyle: mostCommonImport || defaultStyle.importStyle
    }
  }

  /**
   * Generate intelligent JSX based on patterns
   */
  private generateIntelligentJSX(config: {
    componentName: string
    blockType: string
    description: string
    components: string[]
    codeStyle: any
    similarComponents: CodebaseComponent[]
    complexity: string
    requirements?: any
  }): string {
    const { componentName, blockType, components, codeStyle, complexity } = config

    const imports = this.generateImports(components, codeStyle)
    const interfaceName = `${componentName}Props`
    const propsInterface = this.generatePropsInterface(components, blockType)
    const jsxContent = this.generateJSXContent(blockType, components, complexity)

    return `${imports}
import { cn } from '@/lib/utils'

interface ${interfaceName} {
  className?: string
  ${propsInterface}
}

export ${codeStyle.exportStyle === 'default' ? 'default ' : ''}function ${componentName}({
  className,
  ...props
}: ${interfaceName}) {
  return (
    <div className={cn("${this.generateBaseClasses(blockType, complexity)}", className)}>
      ${jsxContent}
    </div>
  )
}`
  }

  // Helper methods
  private filterComponentsByContext(components: CodebaseComponent[], contextPath: string): CodebaseComponent[] {
    const contextDir = contextPath.split('/').slice(0, -1).join('/')
    return components.filter(comp => comp.filePath.startsWith(contextDir))
  }

  private findSimilarComponents(blockType: string, components: CodebaseComponent[]): CodebaseComponent[] {
    return components
      .filter(comp => 
        comp.category === this.mapBlockTypeToCategory(blockType) ||
        comp.name.toLowerCase().includes(blockType.toLowerCase())
      )
      .slice(0, 5)
  }

  private calculateStyleConsistency(block: GeneratedComponent, components: CodebaseComponent[]): number {
    let score = 0
    const totalChecks = 5

    // Check naming convention
    const blockNaming = this.analyzeNamingPattern(block.name)
    const existingNaming = components.map(c => this.analyzeNamingPattern(c.name))
    if (existingNaming.includes(blockNaming)) score++

    // Check component usage
    const blockComponents = block.metadata?.shadcnComponents || []
    const existingComponents = components.flatMap(c => c.shadcnComponents)
    const commonUsage = blockComponents.filter(comp => existingComponents.includes(comp))
    if (commonUsage.length > 0) score++

    // Add more checks...
    score += 3 // Placeholder

    return Math.round((score / totalChecks) * 100)
  }

  private generateIntelligentRecommendations(
    block: GeneratedComponent,
    analysis?: CodebaseAnalysis,
    patterns?: LearnedPatterns
  ): string[] {
    const recommendations: string[] = []

    if (analysis?.shadcnUsage) {
      const topComponents = Object.keys(analysis.shadcnUsage).slice(0, 3)
      const blockComponents = block.metadata?.shadcnComponents || []
      const unusedTop = topComponents.filter(comp => !blockComponents.includes(comp))
      
      if (unusedTop.length > 0) {
        recommendations.push(`Consider using ${unusedTop.join(', ')} - commonly used in your codebase`)
      }
    }

    if (block.metadata?.complexity === 'complex') {
      recommendations.push('Consider breaking this component into smaller, reusable pieces')
    }

    if (patterns?.stylingApproaches.includes('cn utility')) {
      recommendations.push('Great! Using cn utility for consistent styling')
    }

    return recommendations
  }

  // Utility methods
  private mapBlockTypeToCategory(blockType: string): 'layout' | 'content' | 'media' | 'form' | 'navigation' | 'data' {
    const mapping: Record<string, any> = {
      hero: 'content',
      feature: 'content',
      testimonial: 'content',
      pricing: 'data',
      contact: 'form',
      product: 'content',
      content: 'content',
      navigation: 'navigation',
      footer: 'layout'
    }
    return mapping[blockType] || 'content'
  }

  private getBlockSpecificComponents(blockType: string): string[] {
    const mapping: Record<string, string[]> = {
      hero: ['Card', 'Button', 'Badge'],
      feature: ['Card', 'Badge', 'Separator'],
      testimonial: ['Card', 'Avatar', 'Badge'],
      pricing: ['Card', 'Button', 'Badge', 'Separator'],
      contact: ['Card', 'Input', 'Button', 'Textarea', 'Label'],
      product: ['Card', 'Button', 'Badge', 'AspectRatio'],
      content: ['Card', 'Separator'],
      navigation: ['NavigationMenu', 'Button'],
      footer: ['Separator', 'Button']
    }
    return mapping[blockType] || ['Card', 'Button']
  }

  private analyzeNamingPattern(name: string): string {
    if (/^[A-Z][a-z]+(?:[A-Z][a-z]+)*$/.test(name)) return 'PascalCase'
    if (/^[a-z]+(?:[A-Z][a-z]+)*$/.test(name)) return 'camelCase'
    if (/^[a-z]+(?:-[a-z]+)*$/.test(name)) return 'kebab-case'
    return 'mixed'
  }

  private getMostCommon<T>(items: T[]): T | undefined {
    if (items.length === 0) return undefined
    
    const counts = items.reduce((acc, item) => {
      acc[item as any] = (acc[item as any] || 0) + 1
      return acc
    }, {} as Record<any, number>)
    
    return Object.entries(counts)
      .sort(([, a], [, b]) => b - a)[0]?.[0] as T
  }

  private generateComponentName(blockType: string, description: string, namingConvention: string): string {
    const words = description.split(' ').slice(0, 3)
    const baseName = `${blockType}${words.join('')}Block`
    
    switch (namingConvention) {
      case 'PascalCase':
        return baseName.replace(/\b\w/g, l => l.toUpperCase())
      case 'camelCase':
        return baseName.charAt(0).toLowerCase() + baseName.slice(1)
      default:
        return baseName
    }
  }

  private generateImports(components: string[], codeStyle: any): string {
    const quote = codeStyle.quotesStyle === 'single' ? "'" : '"'
    return `import { ${components.join(', ')} } from ${quote}@/components/ui${quote}`
  }

  private generatePropsInterface(components: string[], blockType: string): string {
    // Generate props based on component types and block type
    const props: string[] = []
    
    if (components.includes('Button')) {
      props.push('buttonText?: string', 'onButtonClick?: () => void')
    }
    
    if (blockType === 'hero') {
      props.push('title?: string', 'subtitle?: string', 'description?: string')
    }
    
    return props.join('\n  ')
  }

  private generateJSXContent(blockType: string, components: string[], complexity: string): string {
    // Generate JSX content based on block type and components
    return `{/* Generated ${blockType} content using ${components.join(', ')} */}`
  }

  private generateBaseClasses(blockType: string, complexity: string): string {
    const baseClasses = ['p-6', 'rounded-lg']
    
    if (complexity === 'complex') {
      baseClasses.push('space-y-8')
    } else {
      baseClasses.push('space-y-4')
    }
    
    return baseClasses.join(' ')
  }

  private generatePropertiesFromPatterns(
    _components: string[],
    _similarComponents: CodebaseComponent[],
    _patterns?: LearnedPatterns
  ) {
    // Generate properties configuration based on patterns
    return {
      appearance: { title: 'Appearance', fields: {} },
      content: { title: 'Content', fields: {} },
      behavior: { title: 'Behavior', fields: {} },
      data: { title: 'Data', fields: {} },
      layout: { title: 'Layout', fields: {} }
    }
  }

  private generateDefaultValues(_propertiesConfig: any, _codeStyle: any) {
    return {}
  }

  private extractPropsFromJSX(_jsx: string): Record<string, any> {
    return {}
  }
}

// Export singleton instance
export const intelligentBlockService = new IntelligentBlockService()
