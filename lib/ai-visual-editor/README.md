# AI Visual Editor - Intelligent Block Generation System

A clean, intelligent system for generating shadcn/ui blocks by learning from your existing codebase patterns.

## 🎯 Overview

This system eliminates the need for static templates by dynamically analyzing your codebase to understand:
- Component usage patterns
- Architectural conventions  
- Styling approaches
- Naming conventions

## 🏗️ Core Components

### **Intelligent Block Service** (`services/intelligent-block-service.ts`)
The main orchestrator that:
- Analyzes your codebase for patterns
- Generates blocks that match your existing style
- Provides style consistency scoring
- Offers intelligent recommendations

### **Codebase Analyzer** (`services/codebase-analyzer.ts`)
Discovers and analyzes:
- All React components in your project
- Component relationships and dependencies
- Usage patterns and architectural decisions
- Styling approaches and conventions

### **AI Tools** (`tools/`)
- `intelligent-block-generator.ts` - AI tools for intelligent generation
- `shadcn-block-generation-tools.ts` - Legacy shadcn tools (kept for compatibility)

### **API Routes** (`/api/`)
- `/api/ai-visual-editor/intelligent-blocks` - Main intelligent generation endpoint
- `/api/codebase/analyze-components` - Component analysis API
- `/api/codebase/monitor-components` - Component monitoring API (simplified)

## 🚀 Quick Start

### Basic Usage
```typescript
import { intelligentBlockService } from '@/lib/ai-visual-editor'

// Generate a block that learns from your codebase
const result = await intelligentBlockService.generateIntelligentBlock({
  description: 'Modern pricing section with feature comparison',
  blockType: 'pricing',
  learnFromCodebase: true,
  matchExistingStyle: true
})

console.log('Generated block:', result.block)
console.log('Style consistency:', result.styleConsistency + '%')
```

### Codebase Analysis
```typescript
import { codebaseAnalyzer } from '@/lib/ai-visual-editor'

// Discover all components
const components = await codebaseAnalyzer.discoverComponents()

// Get usage patterns
const patterns = await codebaseAnalyzer.getComponentUsagePatterns()
console.log('Most used shadcn components:', patterns.shadcnUsage)
```

## 🎨 What Makes It Intelligent

### **Template-Free Generation**
- No static templates - everything learned from your codebase
- Adapts to your unique architectural patterns
- Generates components that feel native to your project

### **Style Consistency**
- Analyzes naming conventions (PascalCase, camelCase, etc.)
- Learns import/export patterns
- Matches your code structure and organization
- Provides measurable consistency scores (0-100%)

### **Smart Component Selection**
- Uses actual usage statistics from your codebase
- Prioritizes components you actually use
- Suggests optimal shadcn component combinations

### **Context Awareness**
- Understands your project structure
- Generates components appropriate for their location
- Maintains architectural integrity

## 📊 Features

### **Codebase Learning**
- ✅ Component discovery and analysis
- ✅ Pattern recognition and extraction
- ✅ Usage statistics and trends
- ✅ Architectural pattern analysis

### **Intelligent Generation**
- ✅ Context-aware block generation
- ✅ Style consistency scoring
- ✅ Smart component selection
- ✅ Intelligent recommendations

### **API Integration**
- ✅ RESTful API endpoints
- ✅ AI chat integration
- ✅ Real-time analysis
- ✅ Component monitoring (simplified)

## 🧹 Cleaned Up

This system has been cleaned and optimized by:
- ✅ Removing all redundant template files
- ✅ Eliminating duplicate code and services
- ✅ Fixing all TypeScript errors and warnings
- ✅ Removing unused imports and parameters
- ✅ Simplifying complex dependencies (glob, chokidar)
- ✅ Consolidating functionality into core intelligent system
- ✅ Maintaining only essential, working components

## 🚀 Next Steps

1. **Use the intelligent generation system** instead of static templates
2. **Analyze your codebase** to understand your patterns
3. **Generate blocks** that match your existing style
4. **Monitor consistency scores** to maintain quality
5. **Iterate and improve** based on recommendations

The system learns from your codebase and gets better over time, creating components that truly belong in your project.
