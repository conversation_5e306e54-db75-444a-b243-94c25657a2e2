import { tool } from 'ai'
import { z } from 'zod'

// Shadcn component registry for intelligent selection
const SHADCN_COMPONENTS = {
  layout: ['Card', 'Sheet', 'Dialog', 'Tabs', 'Accordion', 'Collapsible'],
  forms: ['Input', 'Button', 'Select', 'Checkbox', 'RadioGroup', 'Switch', 'Textarea', 'Label'],
  navigation: ['NavigationMenu', 'Breadcrumb', 'Pagination', 'Command'],
  feedback: ['Alert', 'Toast', 'Progress', 'Skeleton', 'Badge'],
  data: ['Table', 'DataTable', 'Calendar', 'DatePicker'],
  media: ['Avatar', 'AspectRatio'],
  overlay: ['Popover', 'Tooltip', 'HoverCard', 'ContextMenu', 'DropdownMenu']
} as const

// Enhanced schema for shadcn block generation
const shadcnBlockSchema = z.object({
  id: z.string(),
  name: z.string(),
  description: z.string(),
  category: z.enum(['hero', 'feature', 'testimonial', 'pricing', 'contact', 'product', 'content', 'navigation', 'footer']),
  shadcnComponents: z.array(z.string()),
  jsx: z.string(),
  props: z.record(z.any()),
  styling: z.object({
    theme: z.enum(['default', 'dark', 'light', 'custom']),
    colorScheme: z.string(),
    spacing: z.enum(['compact', 'normal', 'spacious']),
    borderRadius: z.enum(['none', 'sm', 'md', 'lg', 'xl']),
    shadows: z.boolean()
  }),
  responsive: z.object({
    mobile: z.object({
      columns: z.number(),
      spacing: z.string(),
      typography: z.string()
    }),
    tablet: z.object({
      columns: z.number(),
      spacing: z.string(),
      typography: z.string()
    }),
    desktop: z.object({
      columns: z.number(),
      spacing: z.string(),
      typography: z.string()
    })
  }),
  accessibility: z.object({
    ariaLabels: z.record(z.string()),
    keyboardNavigation: z.boolean(),
    screenReaderSupport: z.boolean(),
    colorContrast: z.enum(['AA', 'AAA'])
  }),
  properties: z.record(z.any()),
  variants: z.array(z.object({
    name: z.string(),
    props: z.record(z.any()),
    description: z.string()
  }))
})

// Tool for generating shadcn-based blocks
export const generateShadcnBlockTool = tool({
  description: 'Generate a high-quality block using shadcn/ui components with intelligent component selection and composition',
  parameters: z.object({
    description: z.string().describe('Detailed description of the block to generate'),
    blockType: z.enum(['hero', 'feature', 'testimonial', 'pricing', 'contact', 'product', 'content', 'navigation', 'footer']),
    primaryComponents: z.array(z.string()).describe('Primary shadcn components to use (e.g., Card, Button, Input)'),
    designStyle: z.enum(['modern', 'minimal', 'bold', 'elegant', 'playful', 'professional']).default('modern'),
    complexity: z.enum(['simple', 'moderate', 'complex']).default('moderate'),
    responsive: z.boolean().default(true),
    accessibility: z.boolean().default(true),
    includeVariants: z.boolean().default(true),
    themeSupport: z.boolean().default(true),
    customStyling: z.object({
      colorScheme: z.string().optional(),
      spacing: z.enum(['compact', 'normal', 'spacious']).optional(),
      borderRadius: z.enum(['none', 'sm', 'md', 'lg', 'xl']).optional(),
      shadows: z.boolean().optional()
    }).optional()
  }),
  execute: async ({ 
    description, 
    blockType, 
    primaryComponents, 
    designStyle, 
    complexity, 
    responsive, 
    accessibility, 
    includeVariants,
    themeSupport,
    customStyling 
  }) => {
    try {
      // Analyze requirements and select optimal shadcn components
      const selectedComponents = selectOptimalComponents(blockType, primaryComponents, complexity)
      
      // Generate block structure
      const blockStructure = generateBlockStructure({
        description,
        blockType,
        components: selectedComponents,
        designStyle,
        complexity,
        responsive,
        accessibility,
        themeSupport
      })

      // Generate JSX with proper shadcn component usage
      const jsx = generateShadcnJSX(blockStructure, selectedComponents, designStyle)

      // Generate properties configuration
      const properties = generateShadcnProperties(selectedComponents, blockStructure)

      // Generate variants if requested
      const variants = includeVariants ? generateBlockVariants(blockStructure, designStyle) : []

      // Apply custom styling
      const styling = {
        theme: themeSupport ? 'default' : 'custom',
        colorScheme: customStyling?.colorScheme || getDefaultColorScheme(designStyle),
        spacing: customStyling?.spacing || 'normal',
        borderRadius: customStyling?.borderRadius || 'md',
        shadows: customStyling?.shadows ?? true
      }

      const block = {
        id: `shadcn-block-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        name: generateBlockName(blockType, description),
        description,
        category: blockType,
        shadcnComponents: selectedComponents,
        jsx,
        props: blockStructure.props,
        styling,
        responsive: generateResponsiveConfig(responsive),
        accessibility: generateAccessibilityConfig(accessibility),
        properties,
        variants
      }

      return {
        success: true,
        block,
        components: selectedComponents,
        message: `Generated ${blockType} block using ${selectedComponents.join(', ')} components`,
        recommendations: generateOptimizationRecommendations(block)
      }
    } catch (error) {
      console.error('Shadcn block generation error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to generate shadcn block',
        message: 'Block generation failed. Please check your requirements and try again.'
      }
    }
  }
})

// Helper functions for component selection and generation
function selectOptimalComponents(blockType: string, primaryComponents: string[], complexity: string): string[] {
  const baseComponents = [...primaryComponents]
  
  // Add recommended components based on block type
  const recommendations = getRecommendedComponents(blockType, complexity)
  
  // Merge and deduplicate
  const allComponents = [...new Set([...baseComponents, ...recommendations])]
  
  // Validate components exist in shadcn
  return allComponents.filter(component => isValidShadcnComponent(component))
}

function getRecommendedComponents(blockType: string, complexity: string): string[] {
  const recommendations: Record<string, string[]> = {
    hero: ['Card', 'Button', 'Badge', 'AspectRatio'],
    feature: ['Card', 'Badge', 'Separator'],
    testimonial: ['Card', 'Avatar', 'Badge'],
    pricing: ['Card', 'Button', 'Badge', 'Separator'],
    contact: ['Card', 'Input', 'Button', 'Textarea', 'Label'],
    product: ['Card', 'Button', 'Badge', 'AspectRatio', 'Separator'],
    content: ['Card', 'Separator', 'Accordion'],
    navigation: ['NavigationMenu', 'Button', 'Sheet'],
    footer: ['Separator', 'Button']
  }

  const base = recommendations[blockType] || ['Card', 'Button']
  
  if (complexity === 'complex') {
    return [...base, 'Tabs', 'Dialog', 'Popover']
  }
  
  return base
}

function isValidShadcnComponent(component: string): boolean {
  return Object.values(SHADCN_COMPONENTS).flat().includes(component as any)
}

function generateBlockStructure(config: any) {
  return {
    type: config.blockType,
    components: config.components,
    layout: determineOptimalLayout(config),
    props: generateDefaultProps(config),
    styling: generateStylingConfig(config),
    interactions: generateInteractionConfig(config)
  }
}

function generateShadcnJSX(structure: any, components: string[], designStyle: string): string {
  // This would generate actual JSX code using the selected components
  // For now, returning a template structure
  return `
import { ${components.join(', ')} } from '@/components/ui'
import { cn } from '@/lib/utils'

interface ${structure.type.charAt(0).toUpperCase() + structure.type.slice(1)}BlockProps {
  className?: string
  // Additional props based on selected components
}

export function ${structure.type.charAt(0).toUpperCase() + structure.type.slice(1)}Block({ 
  className,
  ...props 
}: ${structure.type.charAt(0).toUpperCase() + structure.type.slice(1)}BlockProps) {
  return (
    <div className={cn("${getBaseClasses(designStyle)}", className)}>
      {/* Generated JSX using ${components.join(', ')} */}
    </div>
  )
}
  `.trim()
}

function generateShadcnProperties(components: string[], structure: any) {
  const properties: Record<string, any> = {}
  
  // Generate properties based on selected components
  components.forEach(component => {
    const componentProps = getShadcnComponentProps(component)
    properties[component.toLowerCase()] = componentProps
  })
  
  return properties
}

function generateBlockVariants(structure: any, designStyle: string) {
  return [
    {
      name: 'default',
      props: structure.props,
      description: 'Default variant'
    },
    {
      name: 'compact',
      props: { ...structure.props, spacing: 'compact' },
      description: 'Compact spacing variant'
    },
    {
      name: 'featured',
      props: { ...structure.props, featured: true },
      description: 'Featured/highlighted variant'
    }
  ]
}

// Additional helper functions
function generateBlockName(blockType: string, description: string): string {
  const words = description.split(' ').slice(0, 3)
  return `${blockType.charAt(0).toUpperCase() + blockType.slice(1)} ${words.join(' ')}`
}

function getDefaultColorScheme(designStyle: string): string {
  const schemes: Record<string, string> = {
    modern: 'blue',
    minimal: 'gray',
    bold: 'red',
    elegant: 'purple',
    playful: 'orange',
    professional: 'slate'
  }
  return schemes[designStyle] || 'blue'
}

function generateResponsiveConfig(responsive: boolean) {
  if (!responsive) {
    return {
      mobile: { columns: 1, spacing: 'normal', typography: 'sm' },
      tablet: { columns: 1, spacing: 'normal', typography: 'base' },
      desktop: { columns: 1, spacing: 'normal', typography: 'base' }
    }
  }

  return {
    mobile: { columns: 1, spacing: 'compact', typography: 'sm' },
    tablet: { columns: 2, spacing: 'normal', typography: 'base' },
    desktop: { columns: 3, spacing: 'spacious', typography: 'lg' }
  }
}

function generateAccessibilityConfig(accessibility: boolean) {
  return {
    ariaLabels: accessibility ? generateAriaLabels() : {},
    keyboardNavigation: accessibility,
    screenReaderSupport: accessibility,
    colorContrast: accessibility ? 'AA' : 'AA'
  }
}

function generateOptimizationRecommendations(block: any): string[] {
  const recommendations = []
  
  if (block.shadcnComponents.length > 5) {
    recommendations.push('Consider reducing component complexity for better performance')
  }
  
  if (!block.accessibility.screenReaderSupport) {
    recommendations.push('Enable screen reader support for better accessibility')
  }
  
  if (block.styling.shadows && block.shadcnComponents.includes('Card')) {
    recommendations.push('Consider using Card component shadows instead of custom shadows')
  }
  
  return recommendations
}

// Tool for analyzing existing shadcn components
export const analyzeShadcnComponentTool = tool({
  description: 'Analyze existing shadcn components for reusability and optimization opportunities',
  parameters: z.object({
    componentCode: z.string().describe('The JSX/TSX code of the component to analyze'),
    componentName: z.string().describe('Name of the component'),
    analysisType: z.enum(['structure', 'performance', 'accessibility', 'reusability', 'comprehensive']).default('comprehensive')
  }),
  execute: async ({ componentCode, componentName, analysisType }) => {
    try {
      const analysis = await analyzeShadcnComponent(componentCode, componentName, analysisType)

      return {
        success: true,
        componentName,
        analysis,
        shadcnComponents: extractShadcnComponents(componentCode),
        recommendations: generateComponentRecommendations(analysis),
        optimizations: generateOptimizationSuggestions(analysis),
        message: `Analyzed ${componentName} for ${analysisType} aspects`
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Analysis failed',
        message: 'Component analysis failed. Please check the component code.'
      }
    }
  }
})

// Tool for generating shadcn properties panels
export const generateShadcnPropertiesTool = tool({
  description: 'Generate dynamic properties panel configuration for shadcn components',
  parameters: z.object({
    components: z.array(z.string()).describe('List of shadcn components to generate properties for'),
    blockType: z.string().describe('Type of block these properties are for'),
    includeAdvanced: z.boolean().default(false).describe('Include advanced/expert properties'),
    groupByCategory: z.boolean().default(true).describe('Group properties by category'),
    includePresets: z.boolean().default(true).describe('Include preset configurations')
  }),
  execute: async ({ components, blockType, includeAdvanced, groupByCategory, includePresets }) => {
    try {
      const propertiesConfig = generateAdvancedPropertiesConfig({
        components,
        blockType,
        includeAdvanced,
        groupByCategory,
        includePresets
      })

      return {
        success: true,
        propertiesConfig,
        presets: includePresets ? generatePropertyPresets(components, blockType) : [],
        validation: generatePropertyValidation(propertiesConfig),
        message: `Generated properties configuration for ${components.join(', ')} components`
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Properties generation failed',
        message: 'Failed to generate properties configuration.'
      }
    }
  }
})

// Tool for optimizing shadcn blocks
export const optimizeShadcnBlockTool = tool({
  description: 'Optimize existing shadcn blocks for performance, accessibility, and design consistency',
  parameters: z.object({
    blockCode: z.string().describe('The block code to optimize'),
    optimizationType: z.enum(['performance', 'accessibility', 'design', 'responsive', 'comprehensive']).default('comprehensive'),
    targetMetrics: z.object({
      performanceScore: z.number().optional(),
      accessibilityScore: z.number().optional(),
      designConsistency: z.number().optional()
    }).optional()
  }),
  execute: async ({ blockCode, optimizationType, targetMetrics }) => {
    try {
      const optimization = await optimizeBlock(blockCode, optimizationType, targetMetrics)

      return {
        success: true,
        originalCode: blockCode,
        optimizedCode: optimization.code,
        improvements: optimization.improvements,
        metrics: optimization.metrics,
        recommendations: optimization.recommendations,
        message: `Optimized block for ${optimizationType} with ${optimization.improvements.length} improvements`
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Optimization failed',
        message: 'Block optimization failed. Please check the block code.'
      }
    }
  }
})

// Tool for generating shadcn component variants
export const generateShadcnVariantsTool = tool({
  description: 'Generate multiple variants of a shadcn block for different use cases',
  parameters: z.object({
    baseBlock: z.object({
      jsx: z.string(),
      props: z.record(z.any()),
      components: z.array(z.string())
    }).describe('Base block to generate variants from'),
    variantTypes: z.array(z.enum(['size', 'color', 'layout', 'density', 'style'])).describe('Types of variants to generate'),
    count: z.number().min(1).max(10).default(3).describe('Number of variants per type')
  }),
  execute: async ({ baseBlock, variantTypes, count }) => {
    try {
      const variants = generateBlockVariantsAdvanced(baseBlock, variantTypes, count)

      return {
        success: true,
        baseBlock,
        variants,
        variantTypes,
        totalVariants: variants.length,
        message: `Generated ${variants.length} variants across ${variantTypes.length} categories`
      }
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Variant generation failed',
        message: 'Failed to generate block variants.'
      }
    }
  }
})

// Helper function implementations
async function analyzeShadcnComponent(code: string, name: string, type: string) {
  // Analyze component structure, performance, accessibility, etc.
  return {
    structure: analyzeComponentStructure(code),
    performance: analyzePerformanceMetrics(code),
    accessibility: analyzeAccessibilityFeatures(code),
    reusability: analyzeReusabilityScore(code),
    shadcnUsage: analyzeShadcnUsage(code)
  }
}

function extractShadcnComponents(code: string): string[] {
  const importRegex = /import\s*{([^}]+)}\s*from\s*['"]@\/components\/ui['"]/g
  const components: string[] = []
  let match

  while ((match = importRegex.exec(code)) !== null) {
    const importedComponents = match[1]
      .split(',')
      .map(comp => comp.trim())
      .filter(comp => comp.length > 0)
    components.push(...importedComponents)
  }

  return [...new Set(components)]
}

function generateAdvancedPropertiesConfig(config: any) {
  const { components, blockType, includeAdvanced, groupByCategory } = config
  const properties: Record<string, any> = {}

  if (groupByCategory) {
    properties.appearance = generateAppearanceProperties(components)
    properties.layout = generateLayoutProperties(components)
    properties.content = generateContentProperties(components, blockType)
    properties.behavior = generateBehaviorProperties(components)

    if (includeAdvanced) {
      properties.advanced = generateAdvancedProperties(components)
    }
  } else {
    // Flat structure
    Object.assign(properties, generateFlatProperties(components, includeAdvanced))
  }

  return properties
}

function generatePropertyPresets(components: string[], blockType: string) {
  return [
    {
      name: 'Default',
      description: 'Standard configuration',
      values: getDefaultPropertyValues(components)
    },
    {
      name: 'Compact',
      description: 'Space-efficient layout',
      values: getCompactPropertyValues(components)
    },
    {
      name: 'Featured',
      description: 'Highlighted/prominent display',
      values: getFeaturedPropertyValues(components)
    }
  ]
}

async function optimizeBlock(code: string, type: string, targets?: any) {
  const improvements = []
  let optimizedCode = code

  // Performance optimizations
  if (type === 'performance' || type === 'comprehensive') {
    const perfOptimizations = optimizeForPerformance(code)
    improvements.push(...perfOptimizations.improvements)
    optimizedCode = perfOptimizations.code
  }

  // Accessibility optimizations
  if (type === 'accessibility' || type === 'comprehensive') {
    const a11yOptimizations = optimizeForAccessibility(optimizedCode)
    improvements.push(...a11yOptimizations.improvements)
    optimizedCode = a11yOptimizations.code
  }

  // Design consistency optimizations
  if (type === 'design' || type === 'comprehensive') {
    const designOptimizations = optimizeForDesignConsistency(optimizedCode)
    improvements.push(...designOptimizations.improvements)
    optimizedCode = designOptimizations.code
  }

  return {
    code: optimizedCode,
    improvements,
    metrics: calculateOptimizationMetrics(code, optimizedCode),
    recommendations: generatePostOptimizationRecommendations(optimizedCode)
  }
}

function generateBlockVariantsAdvanced(baseBlock: any, variantTypes: string[], count: number) {
  const variants = []

  variantTypes.forEach(type => {
    for (let i = 0; i < count; i++) {
      const variant = generateVariantByType(baseBlock, type, i)
      variants.push(variant)
    }
  })

  return variants
}

// Placeholder implementations for helper functions
function analyzeComponentStructure(code: string) { return {} }
function analyzePerformanceMetrics(code: string) { return {} }
function analyzeAccessibilityFeatures(code: string) { return {} }
function analyzeReusabilityScore(code: string) { return 0 }
function analyzeShadcnUsage(code: string) { return {} }
function generateComponentRecommendations(analysis: any) { return [] }
function generateOptimizationSuggestions(analysis: any) { return [] }
function generateAppearanceProperties(components: string[]) { return {} }
function generateLayoutProperties(components: string[]) { return {} }
function generateContentProperties(components: string[], blockType: string) { return {} }
function generateBehaviorProperties(components: string[]) { return {} }
function generateAdvancedProperties(components: string[]) { return {} }
function generateFlatProperties(components: string[], includeAdvanced: boolean) { return {} }
function getDefaultPropertyValues(components: string[]) { return {} }
function getCompactPropertyValues(components: string[]) { return {} }
function getFeaturedPropertyValues(components: string[]) { return {} }
function generatePropertyValidation(config: any) { return {} }
function optimizeForPerformance(code: string) { return { code, improvements: [] } }
function optimizeForAccessibility(code: string) { return { code, improvements: [] } }
function optimizeForDesignConsistency(code: string) { return { code, improvements: [] } }
function calculateOptimizationMetrics(original: string, optimized: string) { return {} }
function generatePostOptimizationRecommendations(code: string) { return [] }
function generateVariantByType(baseBlock: any, type: string, index: number) { return {} }

// Original placeholder helper functions
function determineOptimalLayout(config: any) { return 'grid' }
function generateDefaultProps(config: any) { return {} }
function generateStylingConfig(config: any) { return {} }
function generateInteractionConfig(config: any) { return {} }
function getBaseClasses(designStyle: string) { return 'p-6 rounded-lg' }
function getShadcnComponentProps(component: string) { return {} }
function generateAriaLabels() { return {} }
