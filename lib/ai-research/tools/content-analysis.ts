// Content Analysis Tools
// Intelligent content analysis and understanding capabilities

import { tool } from 'ai'
import { z } from 'zod'
import { ContentAnalysis, Topic, Entity, SearchResult } from '../types'
import { BrowserlessClient } from '../../browserless'

const browserless = new BrowserlessClient({
  apiToken: process.env.BROWSERLESS_API_TOKEN || '',
  region: 'sfo'
})

/**
 * Content Analysis Tool
 * Analyzes web content for key insights, sentiment, and structure
 */
export const contentAnalysisTool = tool({
  description: 'Analyze web content for insights, sentiment, topics, and entities',
  parameters: z.object({
    url: z.string().url().describe('URL to analyze'),
    analysisDepth: z.enum(['basic', 'detailed', 'comprehensive']).default('detailed').describe('Analysis depth'),
    includeReadability: z.boolean().default(true).describe('Include readability analysis'),
    includeSentiment: z.boolean().default(true).describe('Include sentiment analysis'),
    includeEntities: z.boolean().default(true).describe('Extract named entities'),
    includeTopics: z.boolean().default(true).describe('Extract topics'),
    includeCredibility: z.boolean().default(true).describe('Assess content credibility'),
    language: z.string().default('en').describe('Content language')
  }),
  execute: async ({ 
    url, 
    analysisDepth, 
    includeReadability, 
    includeSentiment, 
    includeEntities, 
    includeTopics,
    includeCredibility,
    language 
  }) => {
    try {
      // Extract content from URL
      const contentResponse = await browserless.content({
        url,
        waitForSelector: {
          selector: 'body',
          timeout: 15000
        }
      })

      if (!contentResponse.success || !contentResponse.data) {
        throw new Error('Failed to extract content from URL')
      }

      const htmlContent = contentResponse.data.data
      const textContent = extractTextFromHTML(htmlContent)

      // Perform analysis based on requested components
      const analysis: ContentAnalysis = {
        summary: await generateSummary(textContent, analysisDepth),
        keyPoints: await extractKeyPoints(textContent, analysisDepth),
        sentiment: includeSentiment ? await analyzeSentiment(textContent) : 'neutral',
        topics: includeTopics ? await extractTopics(textContent, language) : [],
        entities: includeEntities ? await extractEntities(textContent, language) : [],
        readability: includeReadability ? calculateReadability(textContent) : 0,
        credibility: includeCredibility ? await assessCredibility(url, textContent) : 0,
        factualness: await assessFactualness(textContent)
      }

      // Add bias detection for news content
      if (includeCredibility) {
        analysis.bias = await detectBias(textContent)
      }

      return {
        url,
        analysis,
        contentLength: textContent.length,
        wordCount: textContent.split(/\s+/).length,
        analysisDepth,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      throw new Error(`Content analysis failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

/**
 * Batch Content Analysis Tool
 * Analyze multiple URLs in batch
 */
export const batchContentAnalysisTool = tool({
  description: 'Analyze multiple URLs for content insights in batch',
  parameters: z.object({
    urls: z.array(z.string().url()).describe('URLs to analyze'),
    analysisDepth: z.enum(['basic', 'detailed', 'comprehensive']).default('basic').describe('Analysis depth'),
    maxConcurrency: z.number().default(3).describe('Maximum concurrent analyses'),
    includeComparison: z.boolean().default(false).describe('Include comparative analysis'),
    filterCriteria: z.object({
      minWordCount: z.number().optional(),
      maxWordCount: z.number().optional(),
      requiredTopics: z.array(z.string()).optional(),
      excludeTopics: z.array(z.string()).optional()
    }).optional().describe('Content filtering criteria')
  }),
  execute: async ({ 
    urls, 
    analysisDepth, 
    maxConcurrency, 
    includeComparison,
    filterCriteria 
  }) => {
    const results: Array<{
      url: string
      analysis?: ContentAnalysis
      error?: string
      filtered?: boolean
    }> = []

    // Process URLs in batches
    const batches = chunkArray(urls, maxConcurrency)
    
    for (const batch of batches) {
      const batchPromises = batch.map(async (url) => {
        try {
          const result = await contentAnalysisTool.execute({
            url,
            analysisDepth,
            includeReadability: true,
            includeSentiment: true,
            includeEntities: true,
            includeTopics: true,
            includeCredibility: true,
            language: 'en'
          })

          // Apply filtering if specified
          if (filterCriteria && !passesFilter(result.analysis, filterCriteria)) {
            return { url, filtered: true }
          }

          return { url, analysis: result.analysis }
        } catch (error) {
          return { 
            url, 
            error: error instanceof Error ? error.message : 'Analysis failed' 
          }
        }
      })

      const batchResults = await Promise.all(batchPromises)
      results.push(...batchResults)

      // Small delay between batches
      if (batches.indexOf(batch) < batches.length - 1) {
        await new Promise(resolve => setTimeout(resolve, 1000))
      }
    }

    const successfulResults = results.filter(r => r.analysis && !r.error && !r.filtered)
    
    let comparison = null
    if (includeComparison && successfulResults.length > 1) {
      comparison = await compareContentAnalyses(
        successfulResults.map(r => ({ url: r.url, analysis: r.analysis! }))
      )
    }

    return {
      results,
      summary: {
        total: urls.length,
        successful: successfulResults.length,
        failed: results.filter(r => r.error).length,
        filtered: results.filter(r => r.filtered).length
      },
      comparison,
      timestamp: new Date().toISOString()
    }
  }
})

/**
 * Text Summarization Tool
 * Generate intelligent summaries of text content
 */
export const textSummarizationTool = tool({
  description: 'Generate intelligent summaries of text content',
  parameters: z.object({
    text: z.string().describe('Text to summarize'),
    summaryLength: z.enum(['short', 'medium', 'long']).default('medium').describe('Summary length'),
    summaryType: z.enum(['extractive', 'abstractive', 'bullet_points', 'key_insights']).default('abstractive').describe('Summary type'),
    focusAreas: z.array(z.string()).optional().describe('Specific areas to focus on'),
    language: z.string().default('en').describe('Text language')
  }),
  execute: async ({ 
    text, 
    summaryLength, 
    summaryType, 
    focusAreas,
    language 
  }) => {
    try {
      const wordCount = text.split(/\s+/).length
      
      if (wordCount < 50) {
        return {
          summary: text,
          originalLength: wordCount,
          summaryLength: wordCount,
          compressionRatio: 1,
          type: summaryType
        }
      }

      let summary: string
      let targetLength: number

      // Determine target length
      switch (summaryLength) {
        case 'short':
          targetLength = Math.max(50, Math.floor(wordCount * 0.1))
          break
        case 'medium':
          targetLength = Math.max(100, Math.floor(wordCount * 0.2))
          break
        case 'long':
          targetLength = Math.max(200, Math.floor(wordCount * 0.4))
          break
      }

      // Generate summary based on type
      switch (summaryType) {
        case 'extractive':
          summary = await extractiveSummarization(text, targetLength, focusAreas)
          break
        case 'abstractive':
          summary = await abstractiveSummarization(text, targetLength, focusAreas)
          break
        case 'bullet_points':
          summary = await bulletPointSummarization(text, targetLength, focusAreas)
          break
        case 'key_insights':
          summary = await keyInsightsSummarization(text, targetLength, focusAreas)
          break
      }

      const summaryWordCount = summary.split(/\s+/).length
      const compressionRatio = summaryWordCount / wordCount

      return {
        summary,
        originalLength: wordCount,
        summaryLength: summaryWordCount,
        compressionRatio,
        type: summaryType,
        focusAreas,
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      throw new Error(`Summarization failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

/**
 * Topic Extraction Tool
 * Extract and analyze topics from text content
 */
export const topicExtractionTool = tool({
  description: 'Extract and analyze topics from text content',
  parameters: z.object({
    text: z.string().describe('Text to analyze for topics'),
    maxTopics: z.number().default(10).describe('Maximum number of topics to extract'),
    minRelevance: z.number().default(0.1).describe('Minimum relevance score for topics'),
    includeSubtopics: z.boolean().default(false).describe('Include subtopic analysis'),
    topicCategories: z.array(z.string()).optional().describe('Specific topic categories to focus on'),
    language: z.string().default('en').describe('Text language')
  }),
  execute: async ({ 
    text, 
    maxTopics, 
    minRelevance, 
    includeSubtopics,
    topicCategories,
    language 
  }) => {
    try {
      // Extract topics using various methods
      const topics = await extractTopics(text, language)
      
      // Filter by relevance and limit
      const filteredTopics = topics
        .filter(topic => topic.relevance >= minRelevance)
        .slice(0, maxTopics)

      // Filter by categories if specified
      const categorizedTopics = topicCategories 
        ? filteredTopics.filter(topic => 
            topicCategories.some(category => 
              topic.name.toLowerCase().includes(category.toLowerCase())
            )
          )
        : filteredTopics

      // Extract subtopics if requested
      let subtopics: Record<string, Topic[]> = {}
      if (includeSubtopics) {
        for (const topic of categorizedTopics.slice(0, 5)) { // Limit subtopic analysis
          subtopics[topic.name] = await extractSubtopics(text, topic.name, language)
        }
      }

      // Analyze topic relationships
      const relationships = await analyzeTopicRelationships(categorizedTopics, text)

      return {
        topics: categorizedTopics,
        subtopics: includeSubtopics ? subtopics : undefined,
        relationships,
        totalTopicsFound: topics.length,
        filteredCount: categorizedTopics.length,
        analysis: {
          dominantTopic: categorizedTopics[0]?.name,
          topicDiversity: calculateTopicDiversity(categorizedTopics),
          averageRelevance: categorizedTopics.reduce((sum, t) => sum + t.relevance, 0) / categorizedTopics.length
        },
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      throw new Error(`Topic extraction failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

/**
 * Entity Recognition Tool
 * Extract and classify named entities from text
 */
export const entityRecognitionTool = tool({
  description: 'Extract and classify named entities from text content',
  parameters: z.object({
    text: z.string().describe('Text to analyze for entities'),
    entityTypes: z.array(z.enum(['person', 'organization', 'location', 'event', 'concept', 'product'])).optional().describe('Specific entity types to extract'),
    includeAliases: z.boolean().default(true).describe('Include entity aliases and variations'),
    minConfidence: z.number().default(0.5).describe('Minimum confidence score for entities'),
    resolveEntities: z.boolean().default(false).describe('Resolve entities to knowledge base'),
    language: z.string().default('en').describe('Text language')
  }),
  execute: async ({ 
    text, 
    entityTypes, 
    includeAliases, 
    minConfidence,
    resolveEntities,
    language 
  }) => {
    try {
      // Extract entities
      const entities = await extractEntities(text, language)
      
      // Filter by type if specified
      const filteredEntities = entityTypes 
        ? entities.filter(entity => entityTypes.includes(entity.type))
        : entities

      // Filter by confidence
      const confidentEntities = filteredEntities.filter(entity => 
        (entity as any).confidence >= minConfidence
      )

      // Resolve entities if requested
      let resolvedEntities = confidentEntities
      if (resolveEntities) {
        resolvedEntities = await Promise.all(
          confidentEntities.map(async (entity) => ({
            ...entity,
            resolved: await resolveEntity(entity.name, entity.type)
          }))
        )
      }

      // Find entity relationships
      const relationships = await findEntityRelationships(resolvedEntities, text)

      // Group entities by type
      const entitiesByType = groupEntitiesByType(resolvedEntities)

      return {
        entities: resolvedEntities,
        entitiesByType,
        relationships,
        statistics: {
          totalFound: entities.length,
          afterFiltering: resolvedEntities.length,
          byType: Object.fromEntries(
            Object.entries(entitiesByType).map(([type, ents]) => [type, ents.length])
          ),
          averageConfidence: resolvedEntities.reduce((sum, e) => sum + ((e as any).confidence || 0), 0) / resolvedEntities.length
        },
        timestamp: new Date().toISOString()
      }
    } catch (error) {
      throw new Error(`Entity recognition failed: ${error instanceof Error ? error.message : 'Unknown error'}`)
    }
  }
})

// Helper functions

function extractTextFromHTML(html: string): string {
  // Remove script and style elements
  let text = html.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
  text = text.replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
  
  // Remove HTML tags
  text = text.replace(/<[^>]*>/g, ' ')
  
  // Decode HTML entities
  text = text.replace(/&nbsp;/g, ' ')
  text = text.replace(/&amp;/g, '&')
  text = text.replace(/&lt;/g, '<')
  text = text.replace(/&gt;/g, '>')
  text = text.replace(/&quot;/g, '"')
  text = text.replace(/&#39;/g, "'")
  
  // Clean up whitespace
  text = text.replace(/\s+/g, ' ').trim()
  
  return text
}

async function generateSummary(text: string, depth: string): Promise<string> {
  // Placeholder implementation - would use AI summarization
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10)
  const targetSentences = depth === 'basic' ? 2 : depth === 'detailed' ? 4 : 6
  
  return sentences.slice(0, targetSentences).join('. ') + '.'
}

async function extractKeyPoints(text: string, depth: string): Promise<string[]> {
  // Placeholder implementation - would use AI extraction
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 20)
  const targetPoints = depth === 'basic' ? 3 : depth === 'detailed' ? 5 : 8
  
  return sentences.slice(0, targetPoints).map(s => s.trim())
}

async function analyzeSentiment(text: string): Promise<'positive' | 'negative' | 'neutral' | 'mixed'> {
  // Placeholder implementation - would use sentiment analysis AI
  const positiveWords = ['good', 'great', 'excellent', 'amazing', 'wonderful', 'fantastic']
  const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'disappointing', 'poor']
  
  const words = text.toLowerCase().split(/\s+/)
  const positiveCount = words.filter(word => positiveWords.includes(word)).length
  const negativeCount = words.filter(word => negativeWords.includes(word)).length
  
  if (positiveCount > negativeCount * 1.5) return 'positive'
  if (negativeCount > positiveCount * 1.5) return 'negative'
  if (positiveCount > 0 && negativeCount > 0) return 'mixed'
  return 'neutral'
}

async function extractTopics(text: string, language: string): Promise<Topic[]> {
  // Placeholder implementation - would use topic modeling AI
  const words = text.toLowerCase().split(/\s+/)
  const wordFreq: Record<string, number> = {}
  
  words.forEach(word => {
    if (word.length > 3 && !isStopWord(word)) {
      wordFreq[word] = (wordFreq[word] || 0) + 1
    }
  })
  
  return Object.entries(wordFreq)
    .sort(([,a], [,b]) => b - a)
    .slice(0, 10)
    .map(([word, count]) => ({
      name: word,
      relevance: count / words.length,
      mentions: count,
      sentiment: 0
    }))
}

async function extractEntities(text: string, language: string): Promise<Entity[]> {
  // Placeholder implementation - would use NER AI
  const entities: Entity[] = []
  
  // Simple pattern matching for demonstration
  const patterns = {
    person: /\b[A-Z][a-z]+ [A-Z][a-z]+\b/g,
    organization: /\b[A-Z][a-z]+ (?:Inc|Corp|LLC|Ltd|Company|Corporation)\b/g,
    location: /\b(?:New York|London|Paris|Tokyo|California|Texas)\b/g
  }
  
  Object.entries(patterns).forEach(([type, pattern]) => {
    const matches = text.match(pattern) || []
    matches.forEach(match => {
      entities.push({
        name: match,
        type: type as Entity['type'],
        mentions: (text.match(new RegExp(match, 'g')) || []).length,
        sentiment: 0
      })
    })
  })
  
  return entities
}

function calculateReadability(text: string): number {
  // Simplified Flesch Reading Ease score
  const sentences = text.split(/[.!?]+/).length
  const words = text.split(/\s+/).length
  const syllables = text.split(/[aeiouAEIOU]/).length - 1
  
  if (sentences === 0 || words === 0) return 0
  
  const avgSentenceLength = words / sentences
  const avgSyllablesPerWord = syllables / words
  
  const score = 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllablesPerWord)
  return Math.max(0, Math.min(100, score)) / 100 // Normalize to 0-1
}

async function assessCredibility(url: string, text: string): Promise<number> {
  // Placeholder implementation - would use credibility assessment AI
  let score = 0.5 // Base score
  
  // Domain reputation (simplified)
  const domain = new URL(url).hostname
  const trustedDomains = ['wikipedia.org', 'britannica.com', 'reuters.com', 'bbc.com']
  if (trustedDomains.some(trusted => domain.includes(trusted))) {
    score += 0.3
  }
  
  // Content quality indicators
  if (text.length > 1000) score += 0.1 // Substantial content
  if (text.includes('source:') || text.includes('reference:')) score += 0.1 // Citations
  
  return Math.min(1, score)
}

async function assessFactualness(text: string): Promise<number> {
  // Placeholder implementation - would use fact-checking AI
  const factualIndicators = ['according to', 'research shows', 'study found', 'data indicates']
  const opinionIndicators = ['i think', 'in my opinion', 'i believe', 'personally']
  
  const factualCount = factualIndicators.reduce((count, indicator) => 
    count + (text.toLowerCase().match(new RegExp(indicator, 'g')) || []).length, 0
  )
  
  const opinionCount = opinionIndicators.reduce((count, indicator) => 
    count + (text.toLowerCase().match(new RegExp(indicator, 'g')) || []).length, 0
  )
  
  const totalIndicators = factualCount + opinionCount
  return totalIndicators > 0 ? factualCount / totalIndicators : 0.5
}

async function detectBias(text: string): Promise<'left' | 'right' | 'center' | 'unknown'> {
  // Placeholder implementation - would use bias detection AI
  const leftIndicators = ['progressive', 'liberal', 'social justice', 'climate change']
  const rightIndicators = ['conservative', 'traditional', 'free market', 'law and order']
  
  const leftCount = leftIndicators.reduce((count, indicator) => 
    count + (text.toLowerCase().match(new RegExp(indicator, 'g')) || []).length, 0
  )
  
  const rightCount = rightIndicators.reduce((count, indicator) => 
    count + (text.toLowerCase().match(new RegExp(indicator, 'g')) || []).length, 0
  )
  
  if (leftCount > rightCount * 1.5) return 'left'
  if (rightCount > leftCount * 1.5) return 'right'
  if (leftCount > 0 || rightCount > 0) return 'center'
  return 'unknown'
}

function chunkArray<T>(array: T[], chunkSize: number): T[][] {
  const chunks: T[][] = []
  for (let i = 0; i < array.length; i += chunkSize) {
    chunks.push(array.slice(i, i + chunkSize))
  }
  return chunks
}

function passesFilter(analysis: ContentAnalysis, criteria: any): boolean {
  const wordCount = analysis.summary.split(/\s+/).length
  
  if (criteria.minWordCount && wordCount < criteria.minWordCount) return false
  if (criteria.maxWordCount && wordCount > criteria.maxWordCount) return false
  
  if (criteria.requiredTopics) {
    const hasRequired = criteria.requiredTopics.some((topic: string) =>
      analysis.topics.some(t => t.name.toLowerCase().includes(topic.toLowerCase()))
    )
    if (!hasRequired) return false
  }
  
  if (criteria.excludeTopics) {
    const hasExcluded = criteria.excludeTopics.some((topic: string) =>
      analysis.topics.some(t => t.name.toLowerCase().includes(topic.toLowerCase()))
    )
    if (hasExcluded) return false
  }
  
  return true
}

async function compareContentAnalyses(analyses: Array<{ url: string; analysis: ContentAnalysis }>): Promise<any> {
  // Placeholder implementation for content comparison
  return {
    totalAnalyzed: analyses.length,
    averageSentiment: analyses.reduce((sum, a) => sum + (a.analysis.sentiment === 'positive' ? 1 : a.analysis.sentiment === 'negative' ? -1 : 0), 0) / analyses.length,
    commonTopics: findCommonTopics(analyses.map(a => a.analysis.topics)),
    credibilityRange: {
      min: Math.min(...analyses.map(a => a.analysis.credibility)),
      max: Math.max(...analyses.map(a => a.analysis.credibility)),
      average: analyses.reduce((sum, a) => sum + a.analysis.credibility, 0) / analyses.length
    }
  }
}

function findCommonTopics(topicArrays: Topic[][]): Topic[] {
  // Find topics that appear in multiple analyses
  const topicCounts: Record<string, { count: number; totalRelevance: number }> = {}
  
  topicArrays.forEach(topics => {
    topics.forEach(topic => {
      if (!topicCounts[topic.name]) {
        topicCounts[topic.name] = { count: 0, totalRelevance: 0 }
      }
      topicCounts[topic.name].count++
      topicCounts[topic.name].totalRelevance += topic.relevance
    })
  })
  
  return Object.entries(topicCounts)
    .filter(([, data]) => data.count > 1)
    .map(([name, data]) => ({
      name,
      relevance: data.totalRelevance / data.count,
      mentions: data.count,
      sentiment: 0
    }))
    .sort((a, b) => b.relevance - a.relevance)
}

async function extractiveSummarization(text: string, targetLength: number, focusAreas?: string[]): Promise<string> {
  // Extract most important sentences
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10)
  const scored = sentences.map(sentence => ({
    sentence: sentence.trim(),
    score: calculateSentenceScore(sentence, focusAreas)
  }))
  
  scored.sort((a, b) => b.score - a.score)
  
  let summary = ''
  let wordCount = 0
  
  for (const item of scored) {
    const sentenceWords = item.sentence.split(/\s+/).length
    if (wordCount + sentenceWords <= targetLength) {
      summary += item.sentence + '. '
      wordCount += sentenceWords
    }
  }
  
  return summary.trim()
}

async function abstractiveSummarization(text: string, targetLength: number, focusAreas?: string[]): Promise<string> {
  // Placeholder - would use AI for abstractive summarization
  return extractiveSummarization(text, targetLength, focusAreas)
}

async function bulletPointSummarization(text: string, targetLength: number, focusAreas?: string[]): Promise<string> {
  const keyPoints = await extractKeyPoints(text, 'detailed')
  const wordsPerPoint = Math.floor(targetLength / Math.min(keyPoints.length, 5))
  
  return keyPoints
    .slice(0, 5)
    .map(point => `• ${point.split(/\s+/).slice(0, wordsPerPoint).join(' ')}`)
    .join('\n')
}

async function keyInsightsSummarization(text: string, targetLength: number, focusAreas?: string[]): Promise<string> {
  // Extract key insights and findings
  const insights = []
  const sentences = text.split(/[.!?]+/)
  
  for (const sentence of sentences) {
    if (sentence.toLowerCase().includes('found that') || 
        sentence.toLowerCase().includes('shows that') ||
        sentence.toLowerCase().includes('indicates that') ||
        sentence.toLowerCase().includes('reveals that')) {
      insights.push(sentence.trim())
    }
  }
  
  return insights.slice(0, 3).join('. ') + '.'
}

function calculateSentenceScore(sentence: string, focusAreas?: string[]): number {
  let score = sentence.length / 100 // Base score from length
  
  // Boost for focus areas
  if (focusAreas) {
    focusAreas.forEach(area => {
      if (sentence.toLowerCase().includes(area.toLowerCase())) {
        score += 2
      }
    })
  }
  
  // Boost for important indicators
  const importantPhrases = ['important', 'significant', 'key', 'main', 'primary', 'crucial']
  importantPhrases.forEach(phrase => {
    if (sentence.toLowerCase().includes(phrase)) {
      score += 1
    }
  })
  
  return score
}

async function extractSubtopics(text: string, mainTopic: string, language: string): Promise<Topic[]> {
  // Find subtopics related to the main topic
  const sentences = text.split(/[.!?]+/)
  const relevantSentences = sentences.filter(sentence => 
    sentence.toLowerCase().includes(mainTopic.toLowerCase())
  )
  
  const subtopicText = relevantSentences.join('. ')
  return extractTopics(subtopicText, language)
}

async function analyzeTopicRelationships(topics: Topic[], text: string): Promise<Array<{ topic1: string; topic2: string; strength: number }>> {
  const relationships = []
  
  for (let i = 0; i < topics.length; i++) {
    for (let j = i + 1; j < topics.length; j++) {
      const topic1 = topics[i]
      const topic2 = topics[j]
      
      // Count co-occurrences in sentences
      const sentences = text.split(/[.!?]+/)
      const coOccurrences = sentences.filter(sentence => 
        sentence.toLowerCase().includes(topic1.name.toLowerCase()) &&
        sentence.toLowerCase().includes(topic2.name.toLowerCase())
      ).length
      
      if (coOccurrences > 0) {
        relationships.push({
          topic1: topic1.name,
          topic2: topic2.name,
          strength: coOccurrences / Math.min(topic1.mentions, topic2.mentions)
        })
      }
    }
  }
  
  return relationships.sort((a, b) => b.strength - a.strength)
}

function calculateTopicDiversity(topics: Topic[]): number {
  if (topics.length === 0) return 0
  
  const totalRelevance = topics.reduce((sum, topic) => sum + topic.relevance, 0)
  const entropy = topics.reduce((sum, topic) => {
    const probability = topic.relevance / totalRelevance
    return sum - (probability * Math.log2(probability))
  }, 0)
  
  return entropy / Math.log2(topics.length) // Normalized entropy
}

async function resolveEntity(name: string, type: string): Promise<any> {
  // Placeholder - would resolve entities to knowledge base
  return {
    id: `entity_${name.toLowerCase().replace(/\s+/g, '_')}`,
    description: `${type} entity: ${name}`,
    aliases: [name],
    confidence: 0.8
  }
}

async function findEntityRelationships(entities: Entity[], text: string): Promise<Array<{ entity1: string; entity2: string; relationship: string }>> {
  const relationships = []
  
  for (let i = 0; i < entities.length; i++) {
    for (let j = i + 1; j < entities.length; j++) {
      const entity1 = entities[i]
      const entity2 = entities[j]
      
      // Find sentences containing both entities
      const sentences = text.split(/[.!?]+/)
      const sharedSentences = sentences.filter(sentence => 
        sentence.includes(entity1.name) && sentence.includes(entity2.name)
      )
      
      if (sharedSentences.length > 0) {
        // Determine relationship type (simplified)
        let relationship = 'related'
        if (sharedSentences.some(s => s.includes('CEO') || s.includes('founder'))) {
          relationship = 'leads'
        } else if (sharedSentences.some(s => s.includes('located') || s.includes('based'))) {
          relationship = 'located_in'
        }
        
        relationships.push({
          entity1: entity1.name,
          entity2: entity2.name,
          relationship
        })
      }
    }
  }
  
  return relationships
}

function groupEntitiesByType(entities: Entity[]): Record<string, Entity[]> {
  return entities.reduce((groups, entity) => {
    if (!groups[entity.type]) {
      groups[entity.type] = []
    }
    groups[entity.type].push(entity)
    return groups
  }, {} as Record<string, Entity[]>)
}

function isStopWord(word: string): boolean {
  const stopWords = ['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those']
  return stopWords.includes(word.toLowerCase())
}