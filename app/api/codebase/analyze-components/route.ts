import { NextRequest, NextResponse } from 'next/server'
import { promises as fs } from 'fs'
import path from 'path'
import { glob } from 'glob'

export const maxDuration = 60

interface ComponentAnalysis {
  name: string
  filePath: string
  imports: string[]
  exports: string[]
  props: ComponentProp[]
  shadcnComponents: string[]
  patterns: ComponentPattern[]
  dependencies: string[]
  complexity: 'simple' | 'moderate' | 'complex'
  category: string
  usageExamples: string[]
}

interface ComponentProp {
  name: string
  type: string
  required: boolean
  defaultValue?: string
  description?: string
}

interface ComponentPattern {
  type: 'composition' | 'styling' | 'state' | 'props'
  pattern: string
  frequency: number
  examples: string[]
}

export async function GET(req: NextRequest) {
  try {
    const { searchParams } = new URL(req.url)
    const includeNodeModules = searchParams.get('includeNodeModules') === 'true'
    const componentPath = searchParams.get('path')
    const analysisType = searchParams.get('type') || 'all'

    if (componentPath) {
      // Analyze specific component
      const analysis = await analyzeSpecificComponent(componentPath)
      return NextResponse.json({ success: true, component: analysis })
    }

    // Analyze all components in the codebase
    const analysis = await analyzeCodebaseComponents(includeNodeModules, analysisType)
    
    return NextResponse.json({
      success: true,
      analysis: {
        totalComponents: analysis.length,
        shadcnUsage: extractShadcnUsageStats(analysis),
        patterns: extractCommonPatterns(analysis),
        componentRegistry: buildComponentRegistry(analysis),
        recommendations: generateRecommendations(analysis)
      },
      components: analysis
    })
  } catch (error) {
    console.error('Component analysis error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to analyze components' },
      { status: 500 }
    )
  }
}

export async function POST(req: NextRequest) {
  try {
    const { componentCode, componentName, filePath } = await req.json()
    
    const analysis = await analyzeComponentCode(componentCode, componentName, filePath)
    
    return NextResponse.json({
      success: true,
      analysis
    })
  } catch (error) {
    console.error('Component code analysis error:', error)
    return NextResponse.json(
      { success: false, error: 'Failed to analyze component code' },
      { status: 500 }
    )
  }
}

async function analyzeCodebaseComponents(includeNodeModules: boolean, analysisType: string): Promise<ComponentAnalysis[]> {
  const projectRoot = process.cwd()
  
  // Define patterns to search for React components
  const patterns = [
    'components/**/*.{tsx,jsx}',
    'app/**/*.{tsx,jsx}',
    'lib/**/*.{tsx,jsx}',
    'src/**/*.{tsx,jsx}',
    ...(includeNodeModules ? ['node_modules/@/**/*.{tsx,jsx}'] : [])
  ]

  const componentFiles: string[] = []
  
  for (const pattern of patterns) {
    const files = await glob(pattern, { 
      cwd: projectRoot,
      ignore: [
        '**/node_modules/**',
        '**/.next/**',
        '**/dist/**',
        '**/build/**',
        '**/*.test.{tsx,jsx}',
        '**/*.spec.{tsx,jsx}'
      ]
    })
    componentFiles.push(...files)
  }

  const analyses: ComponentAnalysis[] = []
  
  for (const filePath of componentFiles) {
    try {
      const fullPath = path.join(projectRoot, filePath)
      const content = await fs.readFile(fullPath, 'utf-8')
      
      // Skip files that don't contain React components
      if (!isReactComponent(content)) continue
      
      const analysis = await analyzeComponentCode(content, path.basename(filePath, path.extname(filePath)), filePath)
      analyses.push(analysis)
    } catch (error) {
      console.warn(`Failed to analyze ${filePath}:`, error)
    }
  }

  return analyses
}

async function analyzeSpecificComponent(componentPath: string): Promise<ComponentAnalysis> {
  const projectRoot = process.cwd()
  const fullPath = path.join(projectRoot, componentPath)
  
  const content = await fs.readFile(fullPath, 'utf-8')
  const componentName = path.basename(componentPath, path.extname(componentPath))
  
  return analyzeComponentCode(content, componentName, componentPath)
}

async function analyzeComponentCode(code: string, name: string, filePath: string): Promise<ComponentAnalysis> {
  const imports = extractImports(code)
  const exports = extractExports(code)
  const props = extractProps(code)
  const shadcnComponents = extractShadcnComponents(code)
  const patterns = extractPatterns(code)
  const dependencies = extractDependencies(code)
  const complexity = calculateComplexity(code)
  const category = categorizeComponent(code, name)
  const usageExamples = extractUsageExamples(code)

  return {
    name,
    filePath,
    imports,
    exports,
    props,
    shadcnComponents,
    patterns,
    dependencies,
    complexity,
    category,
    usageExamples
  }
}

function isReactComponent(code: string): boolean {
  const reactPatterns = [
    /import\s+.*React/,
    /from\s+['"]react['"]/,
    /export\s+(default\s+)?function\s+[A-Z]/,
    /export\s+(default\s+)?const\s+[A-Z].*=.*=>/,
    /function\s+[A-Z].*\(.*\).*{.*return.*</,
    /const\s+[A-Z].*=.*\(.*\).*=>/
  ]
  
  return reactPatterns.some(pattern => pattern.test(code))
}

function extractImports(code: string): string[] {
  const importRegex = /import\s+(?:{[^}]+}|\*\s+as\s+\w+|\w+)?\s*(?:,\s*{[^}]+})?\s*from\s+['"]([^'"]+)['"]/g
  const imports: string[] = []
  let match

  while ((match = importRegex.exec(code)) !== null) {
    imports.push(match[1])
  }

  return imports
}

function extractExports(code: string): string[] {
  const exportRegex = /export\s+(?:default\s+)?(?:function|const|class|interface|type)\s+(\w+)/g
  const exports: string[] = []
  let match

  while ((match = exportRegex.exec(code)) !== null) {
    exports.push(match[1])
  }

  return exports
}

function extractProps(code: string): ComponentProp[] {
  const props: ComponentProp[] = []
  
  // Extract interface/type definitions for props
  const interfaceRegex = /interface\s+(\w+Props)\s*{([^}]+)}/g
  const typeRegex = /type\s+(\w+Props)\s*=\s*{([^}]+)}/g
  
  let match
  while ((match = interfaceRegex.exec(code)) !== null) {
    const propsContent = match[2]
    const propMatches = propsContent.match(/(\w+)(\?)?:\s*([^;\n]+)/g)
    
    if (propMatches) {
      propMatches.forEach(propMatch => {
        const [, name, optional, type] = propMatch.match(/(\w+)(\?)?:\s*([^;\n]+)/) || []
        if (name && type) {
          props.push({
            name,
            type: type.trim(),
            required: !optional,
            description: extractPropDescription(code, name)
          })
        }
      })
    }
  }

  return props
}

function extractShadcnComponents(code: string): string[] {
  const shadcnImportRegex = /import\s*{([^}]+)}\s*from\s*['"]@\/components\/ui(?:\/([^'"]+))?['"]/g
  const components: string[] = []
  let match

  while ((match = shadcnImportRegex.exec(code)) !== null) {
    const importedComponents = match[1]
      .split(',')
      .map(comp => comp.trim())
      .filter(comp => comp.length > 0)
    components.push(...importedComponents)
  }

  return [...new Set(components)]
}

function extractPatterns(code: string): ComponentPattern[] {
  const patterns: ComponentPattern[] = []
  
  // Common React patterns
  const patternChecks = [
    { type: 'composition', pattern: 'children prop usage', regex: /children/g },
    { type: 'styling', pattern: 'className prop', regex: /className/g },
    { type: 'styling', pattern: 'cn utility usage', regex: /cn\(/g },
    { type: 'state', pattern: 'useState hook', regex: /useState/g },
    { type: 'state', pattern: 'useEffect hook', regex: /useEffect/g },
    { type: 'props', pattern: 'destructured props', regex: /{\s*[^}]+\s*}/g },
    { type: 'composition', pattern: 'forwardRef usage', regex: /forwardRef/g }
  ]

  patternChecks.forEach(({ type, pattern, regex }) => {
    const matches = code.match(regex)
    if (matches && matches.length > 0) {
      patterns.push({
        type: type as any,
        pattern,
        frequency: matches.length,
        examples: matches.slice(0, 3)
      })
    }
  })

  return patterns
}

function extractDependencies(code: string): string[] {
  const imports = extractImports(code)
  return imports.filter(imp => 
    !imp.startsWith('.') && 
    !imp.startsWith('@/') && 
    imp !== 'react'
  )
}

function calculateComplexity(code: string): 'simple' | 'moderate' | 'complex' {
  const lines = code.split('\n').length
  const hooks = (code.match(/use\w+/g) || []).length
  const conditionals = (code.match(/if\s*\(|switch\s*\(|\?\s*:/g) || []).length
  const loops = (code.match(/for\s*\(|while\s*\(|\.map\(|\.forEach\(/g) || []).length
  
  const complexityScore = lines * 0.1 + hooks * 2 + conditionals * 1.5 + loops * 1.5
  
  if (complexityScore < 20) return 'simple'
  if (complexityScore < 50) return 'moderate'
  return 'complex'
}

function categorizeComponent(code: string, name: string): string {
  const categories = {
    'form': /input|form|field|button|select|checkbox|radio/i,
    'layout': /layout|container|grid|flex|wrapper|section/i,
    'navigation': /nav|menu|breadcrumb|pagination|link/i,
    'data': /table|list|card|item|row|column/i,
    'feedback': /alert|toast|notification|modal|dialog/i,
    'media': /image|video|avatar|icon|logo/i
  }

  for (const [category, regex] of Object.entries(categories)) {
    if (regex.test(name) || regex.test(code)) {
      return category
    }
  }

  return 'general'
}

function extractUsageExamples(code: string): string[] {
  // Extract JSX usage examples from comments or documentation
  const examples: string[] = []
  const exampleRegex = /\/\*\*[\s\S]*?@example([\s\S]*?)\*\//g
  let match

  while ((match = exampleRegex.exec(code)) !== null) {
    examples.push(match[1].trim())
  }

  return examples
}

function extractPropDescription(code: string, propName: string): string | undefined {
  const descRegex = new RegExp(`\\*\\s*${propName}\\s*-\\s*([^\\n]+)`, 'i')
  const match = code.match(descRegex)
  return match ? match[1].trim() : undefined
}

function extractShadcnUsageStats(analyses: ComponentAnalysis[]) {
  const stats = {
    totalShadcnComponents: 0,
    mostUsedComponents: {} as Record<string, number>,
    componentsByCategory: {} as Record<string, number>
  }

  analyses.forEach(analysis => {
    stats.totalShadcnComponents += analysis.shadcnComponents.length
    
    analysis.shadcnComponents.forEach(comp => {
      stats.mostUsedComponents[comp] = (stats.mostUsedComponents[comp] || 0) + 1
    })
    
    stats.componentsByCategory[analysis.category] = (stats.componentsByCategory[analysis.category] || 0) + 1
  })

  return stats
}

function extractCommonPatterns(analyses: ComponentAnalysis[]) {
  const allPatterns: Record<string, number> = {}
  
  analyses.forEach(analysis => {
    analysis.patterns.forEach(pattern => {
      const key = `${pattern.type}:${pattern.pattern}`
      allPatterns[key] = (allPatterns[key] || 0) + pattern.frequency
    })
  })

  return Object.entries(allPatterns)
    .sort(([, a], [, b]) => b - a)
    .slice(0, 10)
    .map(([pattern, frequency]) => ({ pattern, frequency }))
}

function buildComponentRegistry(analyses: ComponentAnalysis[]) {
  return analyses.reduce((registry, analysis) => {
    registry[analysis.name] = {
      filePath: analysis.filePath,
      category: analysis.category,
      complexity: analysis.complexity,
      shadcnComponents: analysis.shadcnComponents,
      props: analysis.props
    }
    return registry
  }, {} as Record<string, any>)
}

function generateRecommendations(analyses: ComponentAnalysis[]): string[] {
  const recommendations: string[] = []
  
  const complexComponents = analyses.filter(a => a.complexity === 'complex')
  if (complexComponents.length > analyses.length * 0.3) {
    recommendations.push('Consider breaking down complex components into smaller, reusable pieces')
  }
  
  const shadcnUsage = analyses.reduce((total, a) => total + a.shadcnComponents.length, 0)
  if (shadcnUsage < analyses.length * 0.5) {
    recommendations.push('Consider using more shadcn/ui components for consistency')
  }
  
  return recommendations
}
